{"version": 3, "file": "sg-map-sdk.debug.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;;;;;;;;;;ACVA;AACA;AACA;;AAE0F;AAEnF,IAAeC,cAAc;EAKlC,SAAAA,eAAYC,QAAa,EAAEC,SAAsB,EAAE;IAAAC,eAAA,OAAAH,cAAA;IAAAI,eAAA,yBAFI,IAAIC,GAAG,CAAC,CAAC;IAG9D,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,oBAAoB,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;EAFE,OAAAC,YAAA,CAAAP,cAAA;IAAAQ,GAAA;IAAAC,KAAA;IAKA;AACF;AACA;IACE,SAAUC,cAAcA,CAACC,aAAkB,EAAEC,IAAY,EAAY;MACnE,OAAO;QACLA,IAAI,EAAJA,IAAI;QACJC,MAAM,EAAE,IAAI;QACZF,aAAa,EAAEA,aAAa,CAACA,aAAa,IAAIA,aAAa;QAC3DG,KAAK,EAAEH,aAAa,CAACG,KAAK;QAC1BC,MAAM,EAAEJ,aAAa,CAACI,MAAM;QAC5BC,QAAQ,EAAEL,aAAa,CAACK;MAC1B,CAAC;IACH;;IAEA;EAAA;IAAAR,GAAA;IAAAC,KAAA,EACA,SAAOQ,YAAYA,CAAA,EAAgB;MACjC,OAAO,IAAI,CAACf,SAAS;IACvB;EAAC;IAAAM,GAAA;IAAAC,KAAA;IAsBD;IACA,SAAOS,EAAEA,CAACN,IAAY,EAAEO,QAAmC,EAAQ;MACjE,IAAI,CAAC,IAAI,CAACC,cAAc,CAACC,GAAG,CAACT,IAAI,CAAC,EAAE;QAClC,IAAI,CAACQ,cAAc,CAACE,GAAG,CAACV,IAAI,EAAE,IAAIW,GAAG,CAAC,CAAC,CAAC;MAC1C;MACA,IAAI,CAACH,cAAc,CAACI,GAAG,CAACZ,IAAI,CAAC,CAAEa,GAAG,CAACN,QAAQ,CAAC;MAC5C,OAAO,IAAI;IACb;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAED,SAAOiB,GAAGA,CAACd,IAAY,EAAEO,QAAoC,EAAQ;MACnE,IAAI,CAAC,IAAI,CAACC,cAAc,CAACC,GAAG,CAACT,IAAI,CAAC,EAAE;QAClC,OAAO,IAAI;MACb;MAEA,IAAMe,SAAS,GAAG,IAAI,CAACP,cAAc,CAACI,GAAG,CAACZ,IAAI,CAAE;MAChD,IAAIO,QAAQ,EAAE;QACZQ,SAAS,CAACC,MAAM,CAACT,QAAQ,CAAC;MAC5B,CAAC,MAAM;QACLQ,SAAS,CAACE,KAAK,CAAC,CAAC;MACnB;MAEA,IAAIF,SAAS,CAACG,IAAI,KAAK,CAAC,EAAE;QACxB,IAAI,CAACV,cAAc,CAACQ,MAAM,CAAChB,IAAI,CAAC;MAClC;MAEA,OAAO,IAAI;IACb;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAOsB,IAAIA,CAACnB,IAAY,EAAEoB,IAAU,EAAQ;MAC1C,IAAI,CAAC,IAAI,CAACZ,cAAc,CAACC,GAAG,CAACT,IAAI,CAAC,EAAE;QAClC,OAAO,IAAI;MACb;MAEA,IAAMqB,KAAe,GAAAC,aAAA;QACnBtB,IAAI,EAAJA,IAAI;QACJC,MAAM,EAAE;MAAI,GACTmB,IAAI,CACR;MAED,IAAI,CAACZ,cAAc,CAACI,GAAG,CAACZ,IAAI,CAAC,CAAEuB,OAAO,CAAC,UAAAhB,QAAQ,EAAI;QACjD,IAAI;UACFA,QAAQ,CAACc,KAAK,CAAC;QACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,gCAAAE,MAAA,CAAgC1B,IAAI,QAAKwB,KAAK,CAAC;QAC9D;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;EAAA;IAAA5B,GAAA;IAAAC,KAAA;IAIA;IACA,SAAO8B,mBAAmBA,CAAA,EAAQ;MAChC,OAAO,IAAI,CAACtC,QAAQ;IACtB;;IAEA;AACF;AACA;EAFE;IAAAO,GAAA;IAAAC,KAAA,EAGA,SAAU+B,mBAAmBA,CAACC,KAAkB,EAAQ;MACtD,IAAI,CAACA,KAAK,CAACC,EAAE,EAAE;QACb,MAAM,IAAI3C,+CAAW,CAAC,uBAAuB,CAAC;MAChD;MACA,IAAI,CAAC0C,KAAK,CAAC7B,IAAI,EAAE;QACf,MAAM,IAAIb,+CAAW,CAAC,wBAAwB,CAAC;MACjD;IACF;;IAEA;AACF;AACA;EAFE;IAAAS,GAAA;IAAAC,KAAA,EAGA,SAAUkC,oBAAoBA,CAACC,MAAoB,EAAQ;MACzD,IAAI,CAACA,MAAM,CAAChC,IAAI,EAAE;QAChB,MAAM,IAAIb,+CAAW,CAAC,yBAAyB,CAAC;MAClD;IACF;;IAEA;AACF;AACA;EAFE;IAAAS,GAAA;IAAAC,KAAA,EAGA,SAAUoC,WAAWA,CAAIC,EAAW,EAAEC,YAAoB,EAAK;MAC7D,IAAI;QACF,OAAOD,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,OAAOV,KAAK,EAAE;QACd,MAAM,IAAIrC,+CAAW,IAAAuC,MAAA,CAAIS,YAAY,QAAAT,MAAA,CAAKF,KAAK,CAACY,OAAO,CAAE,CAAC;MAC5D;IACF;;IAEA;AACF;AACA;EAFE;IAAAxC,GAAA;IAAAC,KAAA,EAGA,SAAUwC,aAAaA,CAAA,EAAS;MAC9B,IAAI,CAAC,IAAI,CAAChD,QAAQ,EAAE;QAClB,MAAM,IAAIF,+CAAW,CAAC,+BAA+B,CAAC;MACxD;IACF;EAAC;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/JH;AACA;AACA;;AAEgD;AAGzC,IAAMmD,aAAa,0BAAAC,eAAA;EAAA,SAAAD,cAAA;IAAA/C,eAAA,OAAA+C,aAAA;IAAA,OAAAE,UAAA,OAAAF,aAAA,EAAAG,SAAA;EAAA;EAAAC,SAAA,CAAAJ,aAAA,EAAAC,eAAA;EAAA,OAAA5C,YAAA,CAAA2C,aAAA;IAAA1C,GAAA;IAAAC,KAAA;IAExB;AACF;AACA;IACE,SAAUH,oBAAoBA,CAAA,EAAS;MAAA,IAAAiD,KAAA;MACrC,IAAI,CAAC,IAAI,CAACtD,QAAQ,EAAE;;MAEpB;MACA,IAAMuD,MAAM,GAAG,CACb,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAC5C,kBAAkB,EAAE,sBAAsB,EAAE,aAAa,EACzD,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,kBAAkB,EAClE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,EAC1D,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAClE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAC1D,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EACzD,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EACxD,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EACrD,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAC3C;MAEDA,MAAM,CAACrB,OAAO,CAAC,UAAAsB,SAAS,EAAI;QAC1BF,KAAI,CAACtD,QAAQ,CAACiB,EAAE,CAACuC,SAAS,EAAE,UAACC,CAAM,EAAK;UACtC,IAAMC,eAAe,GAAGJ,KAAI,CAAC7C,cAAc,CAACgD,CAAC,EAAED,SAAS,CAAC;UACzDF,KAAI,CAACxB,IAAI,CAAC0B,SAAS,EAAEE,eAAe,CAAC;QACvC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;;IAEA;EAAA;IAAAnD,GAAA;IAAAC,KAAA,EACA,SAAOmD,SAASA,CAAA,EAAqB;MACnC,IAAI,CAACX,aAAa,CAAC,CAAC;MACpB,IAAMY,MAAM,GAAG,IAAI,CAAC5D,QAAQ,CAAC2D,SAAS,CAAC,CAAC;MACxC,OAAO,CAACC,MAAM,CAACC,GAAG,EAAED,MAAM,CAACE,GAAG,CAAC;IACjC;EAAC;IAAAvD,GAAA;IAAAC,KAAA,EAED,SAAOuD,SAASA,CAACH,MAAwB,EAAQ;MAAA,IAAAI,MAAA;MAC/C,IAAI,CAAChB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrBoB,MAAI,CAAChE,QAAQ,CAAC+D,SAAS,CAACH,MAAM,CAAC;MACjC,CAAC,EAAE,sBAAsB,CAAC;MAC1B,OAAO,IAAI;IACb;EAAC;IAAArD,GAAA;IAAAC,KAAA,EAED,SAAOyD,OAAOA,CAAA,EAAW;MACvB,IAAI,CAACjB,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAAChD,QAAQ,CAACiE,OAAO,CAAC,CAAC;IAChC;EAAC;IAAA1D,GAAA;IAAAC,KAAA,EAED,SAAO0D,OAAOA,CAACC,IAAY,EAAQ;MAAA,IAAAC,MAAA;MACjC,IAAI,CAACpB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrBwB,MAAI,CAACpE,QAAQ,CAACkE,OAAO,CAACC,IAAI,CAAC;MAC7B,CAAC,EAAE,oBAAoB,CAAC;MACxB,OAAO,IAAI;IACb;EAAC;IAAA5D,GAAA;IAAAC,KAAA,EAED,SAAO6D,UAAUA,CAAA,EAAW;MAC1B,IAAI,CAACrB,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAAChD,QAAQ,CAACqE,UAAU,CAAC,CAAC;IACnC;EAAC;IAAA9D,GAAA;IAAAC,KAAA,EAED,SAAO8D,UAAUA,CAACC,OAAe,EAAQ;MAAA,IAAAC,MAAA;MACvC,IAAI,CAACxB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB4B,MAAI,CAACxE,QAAQ,CAACsE,UAAU,CAACC,OAAO,CAAC;MACnC,CAAC,EAAE,uBAAuB,CAAC;MAC3B,OAAO,IAAI;IACb;EAAC;IAAAhE,GAAA;IAAAC,KAAA,EAED,SAAOiE,QAAQA,CAAA,EAAW;MACxB,IAAI,CAACzB,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAAChD,QAAQ,CAACyE,QAAQ,CAAC,CAAC;IACjC;EAAC;IAAAlE,GAAA;IAAAC,KAAA,EAED,SAAOkE,QAAQA,CAACC,KAAa,EAAQ;MAAA,IAAAC,MAAA;MACnC,IAAI,CAAC5B,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrBgC,MAAI,CAAC5E,QAAQ,CAAC0E,QAAQ,CAACC,KAAK,CAAC;MAC/B,CAAC,EAAE,qBAAqB,CAAC;MACzB,OAAO,IAAI;IACb;;IAEA;EAAA;IAAApE,GAAA;IAAAC,KAAA,EACA,SAAOqE,QAAQA,CAACrC,KAAkB,EAAQ;MAAA,IAAAsC,MAAA;MACxC,IAAI,CAAC9B,aAAa,CAAC,CAAC;MACpB,IAAI,CAACT,mBAAmB,CAACC,KAAK,CAAC;MAE/B,IAAI,CAACI,WAAW,CAAC,YAAM;QACrBkC,MAAI,CAAC9E,QAAQ,CAAC6E,QAAQ,CAACrC,KAAK,CAAC;MAC/B,CAAC,yBAAAH,MAAA,CAAyBG,KAAK,CAACC,EAAE,CAAE,CAAC;MAErC,OAAO,IAAI;IACb;EAAC;IAAAlC,GAAA;IAAAC,KAAA,EAED,SAAOuE,WAAWA,CAACC,OAAe,EAAQ;MAAA,IAAAC,MAAA;MACxC,IAAI,CAACjC,aAAa,CAAC,CAAC;MAEpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB,IAAIqC,MAAI,CAACjF,QAAQ,CAACkF,QAAQ,CAACF,OAAO,CAAC,EAAE;UACnCC,MAAI,CAACjF,QAAQ,CAAC+E,WAAW,CAACC,OAAO,CAAC;QACpC;MACF,CAAC,4BAAA3C,MAAA,CAA4B2C,OAAO,CAAE,CAAC;MAEvC,OAAO,IAAI;IACb;EAAC;IAAAzE,GAAA;IAAAC,KAAA,EAED,SAAO0E,QAAQA,CAACF,OAAe,EAAO;MACpC,IAAI,CAAChC,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAAChD,QAAQ,CAACkF,QAAQ,CAACF,OAAO,CAAC;IACxC;EAAC;IAAAzE,GAAA;IAAAC,KAAA,EAED,SAAO2E,SAASA,CAACH,OAAe,EAAEI,QAAiB,EAAQ;MAAA,IAAAC,MAAA;MACzD,IAAI,CAACrC,aAAa,CAAC,CAAC;MAEpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrByC,MAAI,CAACrF,QAAQ,CAACmF,SAAS,CAACH,OAAO,EAAEI,QAAQ,CAAC;MAC5C,CAAC,0BAAA/C,MAAA,CAA0B2C,OAAO,CAAE,CAAC;MAErC,OAAO,IAAI;IACb;;IAEA;EAAA;IAAAzE,GAAA;IAAAC,KAAA,EACA,SAAO8E,SAASA,CAACC,QAAgB,EAAE5C,MAAoB,EAAQ;MAAA,IAAA6C,MAAA;MAC7D,IAAI,CAACxC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACN,oBAAoB,CAACC,MAAM,CAAC;MAEjC,IAAI,CAACC,WAAW,CAAC,YAAM;QACrB4C,MAAI,CAACxF,QAAQ,CAACsF,SAAS,CAACC,QAAQ,EAAE5C,MAAM,CAAC;MAC3C,CAAC,0BAAAN,MAAA,CAA0BkD,QAAQ,CAAE,CAAC;MAEtC,OAAO,IAAI;IACb;EAAC;IAAAhF,GAAA;IAAAC,KAAA,EAED,SAAOiF,YAAYA,CAACF,QAAgB,EAAQ;MAAA,IAAAG,MAAA;MAC1C,IAAI,CAAC1C,aAAa,CAAC,CAAC;MAEpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB,IAAI8C,MAAI,CAAC1F,QAAQ,CAAC2F,SAAS,CAACJ,QAAQ,CAAC,EAAE;UACrCG,MAAI,CAAC1F,QAAQ,CAACyF,YAAY,CAACF,QAAQ,CAAC;QACtC;MACF,CAAC,6BAAAlD,MAAA,CAA6BkD,QAAQ,CAAE,CAAC;MAEzC,OAAO,IAAI;IACb;EAAC;IAAAhF,GAAA;IAAAC,KAAA,EAED,SAAOmF,SAASA,CAACJ,QAAgB,EAAO;MACtC,IAAI,CAACvC,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAAChD,QAAQ,CAAC2F,SAAS,CAACJ,QAAQ,CAAC;IAC1C;;IAEA;EAAA;IAAAhF,GAAA;IAAAC,KAAA,EACA,SAAOoF,MAAMA,CAAA,EAAS;MAAA,IAAAC,MAAA;MACpB,IAAI,CAAC7C,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrBiD,MAAI,CAAC7F,QAAQ,CAAC4F,MAAM,CAAC,CAAC;MACxB,CAAC,EAAE,sBAAsB,CAAC;MAC1B,OAAO,IAAI;IACb;EAAC;IAAArF,GAAA;IAAAC,KAAA,EAED,SAAOsF,MAAMA,CAAA,EAAS;MAAA,IAAAC,OAAA;MACpB,IAAI,IAAI,CAAC/F,QAAQ,EAAE;QACjB,IAAI,CAAC4C,WAAW,CAAC,YAAM;UACrBmD,OAAI,CAAC/F,QAAQ,CAAC8F,MAAM,CAAC,CAAC;QACxB,CAAC,EAAE,sBAAsB,CAAC;QAC1B,IAAI,CAAC9F,QAAQ,GAAG,IAAI;MACtB;MACA,IAAI,CAACmB,cAAc,CAACS,KAAK,CAAC,CAAC;IAC7B;;IAEA;AACF;AACA;;IAEE;AACF;AACA;EAFE;IAAArB,GAAA;IAAAC,KAAA,EAGA,SAAOwF,QAAQA,CAACC,KAAsB,EAAQ;MAAA,IAAAC,OAAA;MAC5C,IAAI,CAAClD,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrBsD,OAAI,CAAClG,QAAQ,CAACgG,QAAQ,CAACC,KAAK,CAAC;MAC/B,CAAC,EAAE,qBAAqB,CAAC;MACzB,OAAO,IAAI;IACb;;IAEA;AACF;AACA;EAFE;IAAA1F,GAAA;IAAAC,KAAA,EAGA,SAAO2F,QAAQA,CAAA,EAAQ;MACrB,IAAI,CAACnD,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAAChD,QAAQ,CAACmG,QAAQ,CAAC,CAAC;IACjC;;IAEA;AACF;AACA;EAFE;IAAA5F,GAAA;IAAAC,KAAA,EAGA,SAAO4F,KAAKA,CAACC,OAAY,EAAQ;MAAA,IAAAC,OAAA;MAC/B,IAAI,CAACtD,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB0D,OAAI,CAACtG,QAAQ,CAACoG,KAAK,CAACC,OAAO,CAAC;MAC9B,CAAC,EAAE,2BAA2B,CAAC;MAC/B,OAAO,IAAI;IACb;;IAEA;AACF;AACA;EAFE;IAAA9F,GAAA;IAAAC,KAAA,EAGA,SAAO+F,MAAMA,CAACF,OAAY,EAAQ;MAAA,IAAAG,OAAA;MAChC,IAAI,CAACxD,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB4D,OAAI,CAACxG,QAAQ,CAACuG,MAAM,CAACF,OAAO,CAAC;MAC/B,CAAC,EAAE,4BAA4B,CAAC;MAChC,OAAO,IAAI;IACb;;IAEA;AACF;AACA;EAFE;IAAA9F,GAAA;IAAAC,KAAA,EAGA,SAAOiG,SAASA,CAACC,MAA4C,EAAEL,OAAa,EAAQ;MAAA,IAAAM,OAAA;MAClF,IAAI,CAAC3D,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB+D,OAAI,CAAC3G,QAAQ,CAACyG,SAAS,CAACC,MAAM,EAAEL,OAAO,CAAC;MAC1C,CAAC,EAAE,sBAAsB,CAAC;MAC1B,OAAO,IAAI;IACb;;IAEA;AACF;AACA;EAFE;IAAA9F,GAAA;IAAAC,KAAA,EAGA,SAAOoG,qBAAqBA,CAACC,UAAgB,EAAER,OAAa,EAAS;MAAA,IAAAS,OAAA;MACnE,IAAI,CAAC9D,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAACJ,WAAW,CAAC,YAAM;QAC5B,OAAOkE,OAAI,CAAC9G,QAAQ,CAAC4G,qBAAqB,CAACC,UAAU,EAAER,OAAO,CAAC;MACjE,CAAC,EAAE,mCAAmC,CAAC;IACzC;;IAEA;AACF;AACA;EAFE;IAAA9F,GAAA;IAAAC,KAAA,EAGA,SAAOuG,mBAAmBA,CAACxB,QAAgB,EAAEc,OAAa,EAAS;MAAA,IAAAW,OAAA;MACjE,IAAI,CAAChE,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAACJ,WAAW,CAAC,YAAM;QAC5B,OAAOoE,OAAI,CAAChH,QAAQ,CAAC+G,mBAAmB,CAACxB,QAAQ,EAAEc,OAAO,CAAC;MAC7D,CAAC,EAAE,iCAAiC,CAAC;IACvC;;IAEA;AACF;AACA;EAFE;IAAA9F,GAAA;IAAAC,KAAA,EAGA,SAAOyG,SAASA,CAACpG,KAAuB,EAAoB;MAC1D,IAAI,CAACmC,aAAa,CAAC,CAAC;MACpB,IAAMlC,MAAM,GAAG,IAAI,CAACd,QAAQ,CAACiH,SAAS,CAACpG,KAAK,CAAC;MAC7C,OAAO,CAACC,MAAM,CAAC+C,GAAG,EAAE/C,MAAM,CAACgD,GAAG,CAAC;IACjC;;IAEA;AACF;AACA;EAFE;IAAAvD,GAAA;IAAAC,KAAA,EAGA,SAAO0G,OAAOA,CAACpG,MAAwB,EAAoB;MACzD,IAAI,CAACkC,aAAa,CAAC,CAAC;MACpB,IAAMnC,KAAK,GAAG,IAAI,CAACb,QAAQ,CAACkH,OAAO,CAACpG,MAAM,CAAC;MAC3C,OAAO,CAACD,KAAK,CAACsG,CAAC,EAAEtG,KAAK,CAACuG,CAAC,CAAC;IAC3B;EAAC;AAAA,EAvQgCrH,yDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPjD;AACA;AACA;;AAEgD;AAGzC,IAAMsH,iBAAiB,0BAAAnE,eAAA;EAAA,SAAAmE,kBAAA;IAAAnH,eAAA,OAAAmH,iBAAA;IAAA,OAAAlE,UAAA,OAAAkE,iBAAA,EAAAjE,SAAA;EAAA;EAAAC,SAAA,CAAAgE,iBAAA,EAAAnE,eAAA;EAAA,OAAA5C,YAAA,CAAA+G,iBAAA;IAAA9G,GAAA;IAAAC,KAAA;IAE5B;AACF;AACA;IACE,SAAUH,oBAAoBA,CAAA,EAAS;MAAA,IAAAiD,KAAA;MACrC,IAAI,CAAC,IAAI,CAACtD,QAAQ,EAAE;;MAEpB;MACA,IAAMsH,KAAK,GAAG,IAAI,CAACtH,QAAQ,CAACsH,KAAK;MACjC,IAAMC,MAAM,GAAG,IAAI,CAACvH,QAAQ,CAACuH,MAAM;MAEnC,IAAID,KAAK,IAAIA,KAAK,CAACE,MAAM,EAAE;QACzB;QACA,IAAMC,OAAO,GAAG,IAAKC,MAAM,CAASC,UAAU,CAACC,uBAAuB,CAACN,KAAK,CAACE,MAAM,CAAC;;QAEpF;QACAC,OAAO,CAACI,cAAc,CAAC,UAAC7F,KAAU,EAAK;UACrC,IAAM8F,cAAc,GAAGR,KAAK,CAACS,YAAY,CAAC/F,KAAK,CAACgG,QAAQ,CAAC;UACzD,IAAMC,YAAY,GAAIP,MAAM,CAASC,UAAU,CAACO,YAAY,CAACC,aAAa,CAACL,cAAc,CAAC;UAE1F,IAAIG,YAAY,EAAE;YAChB,IAAMG,SAAS,GAAIV,MAAM,CAASC,UAAU,CAACU,IAAI,CAACC,SAAS,CAACL,YAAY,CAACG,SAAS,CAAC;YACnF,IAAMG,QAAQ,GAAIb,MAAM,CAASC,UAAU,CAACU,IAAI,CAACC,SAAS,CAACL,YAAY,CAACM,QAAQ,CAAC;YAEjF,IAAM7E,eAAe,GAAGJ,KAAI,CAAC7C,cAAc,CAAC;cAC1CC,aAAa,EAAEsB,KAAK;cACpBnB,KAAK,EAAE,CAACmB,KAAK,CAACgG,QAAQ,CAACb,CAAC,EAAEnF,KAAK,CAACgG,QAAQ,CAACZ,CAAC,CAAC;cAC3CtG,MAAM,EAAE,CAACsH,SAAS,EAAEG,QAAQ;YAC9B,CAAC,EAAE,OAAO,CAAC;YAEXjF,KAAI,CAACxB,IAAI,CAAC,OAAO,EAAE4B,eAAe,CAAC;UACrC;QACF,CAAC,EAAGgE,MAAM,CAASC,UAAU,CAACa,oBAAoB,CAACC,UAAU,CAAC;;QAE9D;QACAhB,OAAO,CAACI,cAAc,CAAC,UAAC7F,KAAU,EAAK;UACrC,IAAM0B,eAAe,GAAGJ,KAAI,CAAC7C,cAAc,CAAC;YAC1CC,aAAa,EAAEsB,KAAK;YACpBnB,KAAK,EAAE,CAACmB,KAAK,CAAC0G,WAAW,CAACvB,CAAC,EAAEnF,KAAK,CAAC0G,WAAW,CAACtB,CAAC;UAClD,CAAC,EAAE,WAAW,CAAC;UAEf9D,KAAI,CAACxB,IAAI,CAAC,WAAW,EAAE4B,eAAe,CAAC;QACzC,CAAC,EAAGgE,MAAM,CAASC,UAAU,CAACa,oBAAoB,CAACG,UAAU,CAAC;;QAE9D;QACA,IAAIpB,MAAM,EAAE;UACVA,MAAM,CAACqB,SAAS,CAACC,gBAAgB,CAAC,YAAM;YACtCvF,KAAI,CAACxB,IAAI,CAAC,WAAW,EAAE;cAAEnB,IAAI,EAAE,WAAW;cAAEC,MAAM,EAAE0C;YAAK,CAAC,CAAC;UAC7D,CAAC,CAAC;UAEFiE,MAAM,CAACuB,OAAO,CAACD,gBAAgB,CAAC,YAAM;YACpCvF,KAAI,CAACxB,IAAI,CAAC,SAAS,EAAE;cAAEnB,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAE0C;YAAK,CAAC,CAAC;UACzD,CAAC,CAAC;QACJ;;QAEA;QACA,IAAIgE,KAAK,CAACyB,UAAU,EAAE;UACpBzB,KAAK,CAACyB,UAAU,CAACF,gBAAgB,CAAC,YAAM;YACtCvF,KAAI,CAACxB,IAAI,CAAC,QAAQ,EAAE;cAAEnB,IAAI,EAAE,QAAQ;cAAEC,MAAM,EAAE0C;YAAK,CAAC,CAAC;UACvD,CAAC,CAAC;QACJ;MACF;IACF;;IAEA;EAAA;IAAA/C,GAAA;IAAAC,KAAA,EACA,SAAOmD,SAASA,CAAA,EAAqB;MACnC,IAAI,CAACX,aAAa,CAAC,CAAC;MACpB,IAAMuE,MAAM,GAAG,IAAI,CAACvH,QAAQ,CAACuH,MAAM;MACnC,IAAMU,YAAY,GAAIP,MAAM,CAASC,UAAU,CAACO,YAAY,CAACC,aAAa,CAACZ,MAAM,CAACS,QAAQ,CAAC;MAC3F,IAAMI,SAAS,GAAIV,MAAM,CAASC,UAAU,CAACU,IAAI,CAACC,SAAS,CAACL,YAAY,CAACG,SAAS,CAAC;MACnF,IAAMG,QAAQ,GAAIb,MAAM,CAASC,UAAU,CAACU,IAAI,CAACC,SAAS,CAACL,YAAY,CAACM,QAAQ,CAAC;MACjF,OAAO,CAACH,SAAS,EAAEG,QAAQ,CAAC;IAC9B;EAAC;IAAAhI,GAAA;IAAAC,KAAA,EAED,SAAOuD,SAASA,CAACH,MAAwB,EAAQ;MAAA,IAAAI,MAAA;MAC/C,IAAI,CAAChB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB,IAAMoG,WAAW,GAAItB,MAAM,CAASC,UAAU,CAACsB,UAAU,CAACC,WAAW,CAACtF,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACjGI,MAAI,CAAChE,QAAQ,CAACuH,MAAM,CAAC4B,OAAO,CAAC;UAAEH,WAAW,EAAXA;QAAY,CAAC,CAAC;MAC/C,CAAC,EAAE,sBAAsB,CAAC;MAC1B,OAAO,IAAI;IACb;EAAC;IAAAzI,GAAA;IAAAC,KAAA,EAED,SAAOyD,OAAOA,CAAA,EAAW;MACvB,IAAI,CAACjB,aAAa,CAAC,CAAC;MACpB;MACA,IAAMuE,MAAM,GAAG,IAAI,CAACvH,QAAQ,CAACuH,MAAM;MACnC,IAAMU,YAAY,GAAIP,MAAM,CAASC,UAAU,CAACO,YAAY,CAACC,aAAa,CAACZ,MAAM,CAACS,QAAQ,CAAC;MAC3F,IAAMoB,MAAM,GAAGnB,YAAY,CAACmB,MAAM;MAClC;MACA,OAAOf,IAAI,CAACgB,IAAI,CAAC,YAAY,GAAGD,MAAM,CAAC,GAAG,CAAC;IAC7C;EAAC;IAAA7I,GAAA;IAAAC,KAAA,EAED,SAAO0D,OAAOA,CAACC,IAAY,EAAQ;MAAA,IAAAC,MAAA;MACjC,IAAI,CAACpB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB;QACA,IAAMwG,MAAM,GAAG,YAAY,GAAGf,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEnF,IAAI,GAAG,CAAC,CAAC;QACnD,IAAMoD,MAAM,GAAGnD,MAAI,CAACpE,QAAQ,CAACuH,MAAM;QACnC,IAAMU,YAAY,GAAIP,MAAM,CAASC,UAAU,CAACO,YAAY,CAACC,aAAa,CAACZ,MAAM,CAACS,QAAQ,CAAC;QAC3F,IAAMI,SAAS,GAAIV,MAAM,CAASC,UAAU,CAACU,IAAI,CAACC,SAAS,CAACL,YAAY,CAACG,SAAS,CAAC;QACnF,IAAMG,QAAQ,GAAIb,MAAM,CAASC,UAAU,CAACU,IAAI,CAACC,SAAS,CAACL,YAAY,CAACM,QAAQ,CAAC;QAEjF,IAAMS,WAAW,GAAItB,MAAM,CAASC,UAAU,CAACsB,UAAU,CAACC,WAAW,CAACd,SAAS,EAAEG,QAAQ,EAAEa,MAAM,CAAC;QAClG7B,MAAM,CAAC4B,OAAO,CAAC;UAAEH,WAAW,EAAXA;QAAY,CAAC,CAAC;MACjC,CAAC,EAAE,oBAAoB,CAAC;MACxB,OAAO,IAAI;IACb;EAAC;IAAAzI,GAAA;IAAAC,KAAA,EAED,SAAO6D,UAAUA,CAAA,EAAW;MAC1B,IAAI,CAACrB,aAAa,CAAC,CAAC;MACpB,IAAMuE,MAAM,GAAG,IAAI,CAACvH,QAAQ,CAACuH,MAAM;MACnC,OAAQG,MAAM,CAASC,UAAU,CAACU,IAAI,CAACC,SAAS,CAACf,MAAM,CAACgC,OAAO,CAAC;IAClE;EAAC;IAAAhJ,GAAA;IAAAC,KAAA,EAED,SAAO8D,UAAUA,CAACC,OAAe,EAAQ;MAAA,IAAAC,MAAA;MACvC,IAAI,CAACxB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB,IAAM2E,MAAM,GAAG/C,MAAI,CAACxE,QAAQ,CAACuH,MAAM;QACnC,IAAMgC,OAAO,GAAI7B,MAAM,CAASC,UAAU,CAACU,IAAI,CAACmB,SAAS,CAACjF,OAAO,CAAC;QAClEgD,MAAM,CAAC4B,OAAO,CAAC;UACbM,WAAW,EAAE;YACXF,OAAO,EAAPA,OAAO;YACP5E,KAAK,EAAE4C,MAAM,CAAC5C,KAAK;YACnB+E,IAAI,EAAEnC,MAAM,CAACmC;UACf;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,uBAAuB,CAAC;MAC3B,OAAO,IAAI;IACb;EAAC;IAAAnJ,GAAA;IAAAC,KAAA,EAED,SAAOiE,QAAQA,CAAA,EAAW;MACxB,IAAI,CAACzB,aAAa,CAAC,CAAC;MACpB,IAAMuE,MAAM,GAAG,IAAI,CAACvH,QAAQ,CAACuH,MAAM;MACnC,OAAQG,MAAM,CAASC,UAAU,CAACU,IAAI,CAACC,SAAS,CAACf,MAAM,CAAC5C,KAAK,CAAC;IAChE;EAAC;IAAApE,GAAA;IAAAC,KAAA,EAED,SAAOkE,QAAQA,CAACC,KAAa,EAAQ;MAAA,IAAAC,MAAA;MACnC,IAAI,CAAC5B,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB,IAAM2E,MAAM,GAAG3C,MAAI,CAAC5E,QAAQ,CAACuH,MAAM;QACnC,IAAMoC,YAAY,GAAIjC,MAAM,CAASC,UAAU,CAACU,IAAI,CAACmB,SAAS,CAAC7E,KAAK,CAAC;QACrE4C,MAAM,CAAC4B,OAAO,CAAC;UACbM,WAAW,EAAE;YACXF,OAAO,EAAEhC,MAAM,CAACgC,OAAO;YACvB5E,KAAK,EAAEgF,YAAY;YACnBD,IAAI,EAAEnC,MAAM,CAACmC;UACf;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,qBAAqB,CAAC;MACzB,OAAO,IAAI;IACb;;IAEA;EAAA;IAAAnJ,GAAA;IAAAC,KAAA,EACA,SAAOqE,QAAQA,CAACrC,KAAkB,EAAQ;MAAA,IAAAsC,MAAA;MACxC,IAAI,CAAC9B,aAAa,CAAC,CAAC;MACpB,IAAI,CAACT,mBAAmB,CAACC,KAAK,CAAC;MAE/B,IAAI,CAACI,WAAW,CAAC,YAAM;QACrB,IAAM0E,KAAK,GAAGxC,MAAI,CAAC9E,QAAQ,CAACsH,KAAK;;QAEjC;QACA,QAAQ9E,KAAK,CAAC7B,IAAI;UAChB,KAAK,SAAS;YACZmE,MAAI,CAAC8E,eAAe,CAACpH,KAAK,CAAC;YAC3B;UACF,KAAK,KAAK;YACRsC,MAAI,CAAC+E,WAAW,CAACrH,KAAK,CAAC;YACvB;UACF,KAAK,SAAS;YACZsC,MAAI,CAACgF,eAAe,CAACtH,KAAK,CAAC;YAC3B;UACF,KAAK,KAAK;YACRsC,MAAI,CAACiF,WAAW,CAACvH,KAAK,CAAC;YACvB;UACF;YACEJ,OAAO,CAAC4H,IAAI,eAAA3H,MAAA,CAAeG,KAAK,CAAC7B,IAAI,iCAA8B,CAAC;QACxE;MACF,CAAC,yBAAA0B,MAAA,CAAyBG,KAAK,CAACC,EAAE,CAAE,CAAC;MAErC,OAAO,IAAI;IACb;EAAC;IAAAlC,GAAA;IAAAC,KAAA,EAED,SAAQoJ,eAAeA,CAACpH,KAAkB,EAAQ;MAChD,IAAM8E,KAAK,GAAG,IAAI,CAACtH,QAAQ,CAACsH,KAAK;MACjC,IAAM2C,aAAa,GAAG,IAAI,CAACjK,QAAQ,CAACiK,aAAa;MAEjD,IAAIzH,KAAK,CAACG,MAAM,IAAIuH,OAAA,CAAO1H,KAAK,CAACG,MAAM,MAAK,QAAQ,IAAKH,KAAK,CAACG,MAAM,CAASwH,GAAG,EAAE;QACjF,IAAMC,QAAQ,GAAG,IAAK1C,MAAM,CAASC,UAAU,CAAC0C,uBAAuB,CAAC;UACtEF,GAAG,EAAG3H,KAAK,CAACG,MAAM,CAASwH;QAC7B,CAAC,CAAC;QACFF,aAAa,CAACK,kBAAkB,CAACF,QAAQ,CAAC;MAC5C;IACF;EAAC;IAAA7J,GAAA;IAAAC,KAAA,EAED,SAAQqJ,WAAWA,CAACrH,KAAkB,EAAQ;MAC5C,IAAM8E,KAAK,GAAG,IAAI,CAACtH,QAAQ,CAACsH,KAAK;MAEjC,IAAI9E,KAAK,CAACG,MAAM,IAAIuH,OAAA,CAAO1H,KAAK,CAACG,MAAM,MAAK,QAAQ,IAAKH,KAAK,CAACG,MAAM,CAASwH,GAAG,EAAE;QACjF7C,KAAK,CAACiD,qBAAqB,CAAE/H,KAAK,CAACG,MAAM,CAASwH,GAAG,EAAE;UACrDK,IAAI,EAAEhI,KAAK,CAACC;QACd,CAAC,CAAC;MACJ;IACF;EAAC;IAAAlC,GAAA;IAAAC,KAAA,EAED,SAAQsJ,eAAeA,CAACtH,KAAkB,EAAQ;MAChD,IAAIA,KAAK,CAACG,MAAM,IAAIuH,OAAA,CAAO1H,KAAK,CAACG,MAAM,MAAK,QAAQ,IAAKH,KAAK,CAACG,MAAM,CAASwH,GAAG,EAAE;QACjF,IAAMM,eAAe,GAAG,IAAK/C,MAAM,CAASC,UAAU,CAAC+C,uBAAuB,CAAC;UAC7EP,GAAG,EAAG3H,KAAK,CAACG,MAAM,CAASwH,GAAG;UAC9BQ,KAAK,EAAE;QACT,CAAC,CAAC;QACF,IAAI,CAAC3K,QAAQ,CAACyK,eAAe,GAAGA,eAAe;MACjD;IACF;EAAC;IAAAlK,GAAA;IAAAC,KAAA,EAED,SAAQuJ,WAAWA,CAACvH,KAAkB,EAAQ;MAC5C,IAAM8E,KAAK,GAAG,IAAI,CAACtH,QAAQ,CAACsH,KAAK;MAEjC,IAAI9E,KAAK,CAACG,MAAM,IAAIuH,OAAA,CAAO1H,KAAK,CAACG,MAAM,MAAK,QAAQ,IAAKH,KAAK,CAACG,MAAM,CAASwH,GAAG,EAAE;QACjF7C,KAAK,CAACsD,iBAAiB,CAAC;UACtBT,GAAG,EAAG3H,KAAK,CAACG,MAAM,CAASwH,GAAG;UAC9BK,IAAI,EAAEhI,KAAK,CAACC,EAAE;UACdoI,MAAM,EAAE,IAAI,CAAC7K;QACf,CAAC,CAAC;MACJ;IACF;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAED,SAAOuE,WAAWA,CAACC,OAAe,EAAQ;MAAA,IAAAC,MAAA;MACxC,IAAI,CAACjC,aAAa,CAAC,CAAC;MAEpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB;QACA,IAAM0E,KAAK,GAAGrC,MAAI,CAACjF,QAAQ,CAACsH,KAAK;QACjC,IAAM2C,aAAa,GAAGhF,MAAI,CAACjF,QAAQ,CAACiK,aAAa;;QAEjD;QACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,aAAa,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;UAC7C,IAAMtI,KAAK,GAAGyH,aAAa,CAAC1I,GAAG,CAACuJ,CAAC,CAAC;UAClC,IAAItI,KAAK,CAACgI,IAAI,KAAKxF,OAAO,EAAE;YAC1BiF,aAAa,CAACnE,MAAM,CAACtD,KAAK,CAAC;YAC3B;UACF;QACF;;QAEA;QACA,IAAI8E,KAAK,CAAC0D,MAAM,EAAE;UAChB1D,KAAK,CAAC0D,MAAM,CAACC,SAAS,CAAC,CAAC;QAC1B;MACF,CAAC,4BAAA5I,MAAA,CAA4B2C,OAAO,CAAE,CAAC;MAEvC,OAAO,IAAI;IACb;EAAC;IAAAzE,GAAA;IAAAC,KAAA,EAED,SAAO0E,QAAQA,CAACF,OAAe,EAAO;MACpC,IAAI,CAAChC,aAAa,CAAC,CAAC;MACpB;MACA,OAAO,IAAI;IACb;EAAC;IAAAzC,GAAA;IAAAC,KAAA,EAED,SAAO2E,SAASA,CAACH,OAAe,EAAEI,QAAiB,EAAQ;MACzD,IAAI,CAACpC,aAAa,CAAC,CAAC;MACpBZ,OAAO,CAAC4H,IAAI,CAAC,6CAA6C,CAAC;MAC3D,OAAO,IAAI;IACb;;IAEA;EAAA;IAAAzJ,GAAA;IAAAC,KAAA,EACA,SAAO8E,SAASA,CAACC,QAAgB,EAAE5C,MAAoB,EAAQ;MAC7D,IAAI,CAACK,aAAa,CAAC,CAAC;MACpB,IAAI,CAACN,oBAAoB,CAACC,MAAM,CAAC;;MAEjC;MACAP,OAAO,CAAC4H,IAAI,CAAC,6DAA6D,CAAC;MAC3E,OAAO,IAAI;IACb;EAAC;IAAAzJ,GAAA;IAAAC,KAAA,EAED,SAAOiF,YAAYA,CAACF,QAAgB,EAAQ;MAC1C,IAAI,CAACvC,aAAa,CAAC,CAAC;MACpBZ,OAAO,CAAC4H,IAAI,CAAC,6DAA6D,CAAC;MAC3E,OAAO,IAAI;IACb;EAAC;IAAAzJ,GAAA;IAAAC,KAAA,EAED,SAAOmF,SAASA,CAACJ,QAAgB,EAAO;MACtC,IAAI,CAACvC,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI;IACb;;IAEA;EAAA;IAAAzC,GAAA;IAAAC,KAAA,EACA,SAAOoF,MAAMA,CAAA,EAAS;MAAA,IAAAP,MAAA;MACpB,IAAI,CAACrC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrByC,MAAI,CAACrF,QAAQ,CAAC4F,MAAM,CAAC,CAAC;MACxB,CAAC,EAAE,wBAAwB,CAAC;MAC5B,OAAO,IAAI;IACb;EAAC;IAAArF,GAAA;IAAAC,KAAA,EAED,SAAOsF,MAAMA,CAAA,EAAS;MAAA,IAAAN,MAAA;MACpB,IAAI,IAAI,CAACxF,QAAQ,EAAE;QACjB,IAAI,CAAC4C,WAAW,CAAC,YAAM;UACrB4C,MAAI,CAACxF,QAAQ,CAACkL,OAAO,CAAC,CAAC;QACzB,CAAC,EAAE,yBAAyB,CAAC;QAC7B,IAAI,CAAClL,QAAQ,GAAG,IAAI;MACtB;MACA,IAAI,CAACmB,cAAc,CAACS,KAAK,CAAC,CAAC;IAC7B;;IAEA;AACF;AACA;;IAEE;AACF;AACA;EAFE;IAAArB,GAAA;IAAAC,KAAA,EAGA,SAAO4F,KAAKA,CAACC,OAAY,EAAQ;MAAA,IAAAX,MAAA;MAC/B,IAAI,CAAC1C,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB,IAAM2E,MAAM,GAAG7B,MAAI,CAAC1F,QAAQ,CAACuH,MAAM;QACnCA,MAAM,CAACnB,KAAK,CAACC,OAAO,CAAC;MACvB,CAAC,EAAE,2BAA2B,CAAC;MAC/B,OAAO,IAAI;IACb;;IAEA;AACF;AACA;EAFE;IAAA9F,GAAA;IAAAC,KAAA,EAGA,SAAO2I,OAAOA,CAAC9C,OAAY,EAAQ;MAAA,IAAAR,MAAA;MACjC,IAAI,CAAC7C,aAAa,CAAC,CAAC;MACpB,IAAI,CAACJ,WAAW,CAAC,YAAM;QACrB,IAAM2E,MAAM,GAAG1B,MAAI,CAAC7F,QAAQ,CAACuH,MAAM;QACnCA,MAAM,CAAC4B,OAAO,CAAC9C,OAAO,CAAC;MACzB,CAAC,EAAE,oBAAoB,CAAC;MACxB,OAAO,IAAI;IACb;;IAEA;AACF;AACA;EAFE;IAAA9F,GAAA;IAAAC,KAAA,EAGA,SAAO2K,QAAQA,CAAA,EAAQ;MACrB,IAAI,CAACnI,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAAChD,QAAQ,CAACsH,KAAK;IAC5B;;IAEA;AACF;AACA;EAFE;IAAA/G,GAAA;IAAAC,KAAA,EAGA,SAAO4K,SAASA,CAAA,EAAQ;MACtB,IAAI,CAACpI,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI,CAAChD,QAAQ,CAACuH,MAAM;IAC7B;EAAC;AAAA,EA5VoCxH,yDAAc;;;;;;;;;;;;;;;;;;;;;;0BCNrD,uKAAA0D,CAAA,EAAA4H,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAb,EAAAQ,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAZ,CAAA,QAAAc,CAAA,GAAAJ,CAAA,IAAAA,CAAA,CAAAK,SAAA,YAAAC,SAAA,GAAAN,CAAA,GAAAM,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAT,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAZ,CAAA,EAAAc,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAV,CAAA,QAAAtE,CAAA,OAAAiF,CAAA,KAAAD,CAAA,KAAAZ,CAAA,KAAAc,CAAA,EAAA7I,CAAA,EAAA8I,CAAA,EAAAC,CAAA,EAAAL,CAAA,EAAAK,CAAA,CAAAC,IAAA,CAAAhJ,CAAA,MAAA+I,CAAA,WAAAA,EAAAnB,CAAA,EAAAC,CAAA,WAAAR,CAAA,GAAAO,CAAA,EAAAO,CAAA,MAAAG,CAAA,GAAAtI,CAAA,EAAA4I,CAAA,CAAAb,CAAA,GAAAF,CAAA,EAAAiB,CAAA,gBAAAC,EAAAlB,CAAA,EAAAE,CAAA,SAAAI,CAAA,GAAAN,CAAA,EAAAS,CAAA,GAAAP,CAAA,EAAAH,CAAA,OAAAjE,CAAA,IAAA+E,CAAA,KAAAT,CAAA,IAAAL,CAAA,GAAAe,CAAA,CAAArB,MAAA,EAAAM,CAAA,UAAAK,CAAA,EAAAZ,CAAA,GAAAsB,CAAA,CAAAf,CAAA,GAAAmB,CAAA,GAAAH,CAAA,CAAAD,CAAA,EAAAM,CAAA,GAAA5B,CAAA,KAAAQ,CAAA,QAAAI,CAAA,GAAAgB,CAAA,KAAAlB,CAAA,MAAAO,CAAA,GAAAjB,CAAA,EAAAc,CAAA,GAAAd,CAAA,YAAAc,CAAA,WAAAd,CAAA,MAAAA,CAAA,MAAArH,CAAA,IAAAqH,CAAA,OAAA0B,CAAA,MAAAd,CAAA,GAAAJ,CAAA,QAAAkB,CAAA,GAAA1B,CAAA,QAAAc,CAAA,MAAAS,CAAA,CAAAC,CAAA,GAAAd,CAAA,EAAAa,CAAA,CAAAb,CAAA,GAAAV,CAAA,OAAA0B,CAAA,GAAAE,CAAA,KAAAhB,CAAA,GAAAJ,CAAA,QAAAR,CAAA,MAAAU,CAAA,IAAAA,CAAA,GAAAkB,CAAA,MAAA5B,CAAA,MAAAQ,CAAA,EAAAR,CAAA,MAAAU,CAAA,EAAAa,CAAA,CAAAb,CAAA,GAAAkB,CAAA,EAAAd,CAAA,cAAAF,CAAA,IAAAJ,CAAA,aAAAiB,CAAA,QAAAnF,CAAA,OAAAoE,CAAA,qBAAAE,CAAA,EAAAU,CAAA,EAAAM,CAAA,QAAAP,CAAA,YAAAQ,SAAA,uCAAAvF,CAAA,UAAAgF,CAAA,IAAAI,CAAA,CAAAJ,CAAA,EAAAM,CAAA,GAAAd,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAW,CAAA,GAAArB,CAAA,GAAAO,CAAA,OAAAnI,CAAA,GAAAsI,CAAA,MAAA3E,CAAA,KAAA0D,CAAA,KAAAc,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAS,CAAA,CAAAb,CAAA,QAAAgB,CAAA,CAAAZ,CAAA,EAAAG,CAAA,KAAAM,CAAA,CAAAb,CAAA,GAAAO,CAAA,GAAAM,CAAA,CAAAC,CAAA,GAAAP,CAAA,aAAAI,CAAA,MAAArB,CAAA,QAAAc,CAAA,KAAAF,CAAA,YAAAL,CAAA,GAAAP,CAAA,CAAAY,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAAuB,IAAA,CAAA9B,CAAA,EAAAiB,CAAA,UAAAY,SAAA,2CAAAtB,CAAA,CAAAwB,IAAA,SAAAxB,CAAA,EAAAU,CAAA,GAAAV,CAAA,CAAA7K,KAAA,EAAAoL,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAP,CAAA,GAAAP,CAAA,CAAAgC,MAAA,KAAAzB,CAAA,CAAAuB,IAAA,CAAA9B,CAAA,GAAAc,CAAA,SAAAG,CAAA,GAAAY,SAAA,uCAAAjB,CAAA,gBAAAE,CAAA,OAAAd,CAAA,GAAArH,CAAA,cAAA4H,CAAA,IAAAjE,CAAA,GAAAiF,CAAA,CAAAb,CAAA,QAAAO,CAAA,GAAAT,CAAA,CAAAsB,IAAA,CAAApB,CAAA,EAAAa,CAAA,OAAAE,CAAA,kBAAAlB,CAAA,IAAAP,CAAA,GAAArH,CAAA,EAAAmI,CAAA,MAAAG,CAAA,GAAAV,CAAA,cAAAc,CAAA,mBAAA3L,KAAA,EAAA6K,CAAA,EAAAwB,IAAA,EAAAzF,CAAA,SAAAkE,CAAA,EAAAI,CAAA,EAAAZ,CAAA,QAAAiB,CAAA,QAAAQ,CAAA,gBAAAT,UAAA,cAAAiB,kBAAA,cAAAC,2BAAA,KAAA3B,CAAA,GAAAW,MAAA,CAAAiB,cAAA,MAAArB,CAAA,MAAAJ,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAU,mBAAA,CAAAb,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAU,CAAA,GAAAiB,0BAAA,CAAAnB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAA1I,CAAA,WAAAuI,MAAA,CAAAkB,cAAA,GAAAlB,MAAA,CAAAkB,cAAA,CAAAzJ,CAAA,EAAAuJ,0BAAA,KAAAvJ,CAAA,CAAA0J,SAAA,GAAAH,0BAAA,EAAAd,mBAAA,CAAAzI,CAAA,EAAAiI,CAAA,yBAAAjI,CAAA,CAAAoI,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAtI,CAAA,WAAAsJ,iBAAA,CAAAlB,SAAA,GAAAmB,0BAAA,EAAAd,mBAAA,CAAAH,CAAA,iBAAAiB,0BAAA,GAAAd,mBAAA,CAAAc,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAAlB,mBAAA,CAAAc,0BAAA,EAAAtB,CAAA,wBAAAQ,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAL,CAAA,gBAAAQ,mBAAA,CAAAH,CAAA,EAAAP,CAAA,iCAAAU,mBAAA,CAAAH,CAAA,8DAAAsB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAAxC,CAAA,EAAAyC,CAAA,EAAApB,CAAA;AAAA,SAAAD,oBAAAzI,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAP,CAAA,GAAAkB,MAAA,CAAAwB,cAAA,QAAA1C,CAAA,uBAAArH,CAAA,IAAAqH,CAAA,QAAAoB,mBAAA,YAAAuB,mBAAAhK,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAAR,CAAA,GAAAA,CAAA,CAAArH,CAAA,EAAA6H,CAAA,IAAA9K,KAAA,EAAAgL,CAAA,EAAAkC,UAAA,GAAArC,CAAA,EAAAsC,YAAA,GAAAtC,CAAA,EAAAuC,QAAA,GAAAvC,CAAA,MAAA5H,CAAA,CAAA6H,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAU,mBAAA,CAAAzI,CAAA,EAAA6H,CAAA,YAAA7H,CAAA,gBAAAoK,OAAA,CAAAvC,CAAA,EAAAE,CAAA,EAAA/H,CAAA,UAAAiI,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAQ,mBAAA,CAAAzI,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAAyC,mBAAAtC,CAAA,EAAAH,CAAA,EAAA5H,CAAA,EAAA6H,CAAA,EAAAI,CAAA,EAAAa,CAAA,EAAAX,CAAA,cAAAd,CAAA,GAAAU,CAAA,CAAAe,CAAA,EAAAX,CAAA,GAAAG,CAAA,GAAAjB,CAAA,CAAAtK,KAAA,WAAAgL,CAAA,gBAAA/H,CAAA,CAAA+H,CAAA,KAAAV,CAAA,CAAA+B,IAAA,GAAAxB,CAAA,CAAAU,CAAA,IAAAgC,OAAA,CAAAC,OAAA,CAAAjC,CAAA,EAAAkC,IAAA,CAAA3C,CAAA,EAAAI,CAAA;AAAA,SAAAwC,kBAAA1C,CAAA,6BAAAH,CAAA,SAAA5H,CAAA,GAAAL,SAAA,aAAA2K,OAAA,WAAAzC,CAAA,EAAAI,CAAA,QAAAa,CAAA,GAAAf,CAAA,CAAA2C,KAAA,CAAA9C,CAAA,EAAA5H,CAAA,YAAA2K,MAAA5C,CAAA,IAAAsC,kBAAA,CAAAvB,CAAA,EAAAjB,CAAA,EAAAI,CAAA,EAAA0C,KAAA,EAAAC,MAAA,UAAA7C,CAAA,cAAA6C,OAAA7C,CAAA,IAAAsC,kBAAA,CAAAvB,CAAA,EAAAjB,CAAA,EAAAI,CAAA,EAAA0C,KAAA,EAAAC,MAAA,WAAA7C,CAAA,KAAA4C,KAAA;AAAA,SAAAE,QAAA7K,CAAA,EAAA6H,CAAA,QAAAD,CAAA,GAAAW,MAAA,CAAAuC,IAAA,CAAA9K,CAAA,OAAAuI,MAAA,CAAAwC,qBAAA,QAAA9C,CAAA,GAAAM,MAAA,CAAAwC,qBAAA,CAAA/K,CAAA,GAAA6H,CAAA,KAAAI,CAAA,GAAAA,CAAA,CAAA+C,MAAA,WAAAnD,CAAA,WAAAU,MAAA,CAAA0C,wBAAA,CAAAjL,CAAA,EAAA6H,CAAA,EAAAoC,UAAA,OAAArC,CAAA,CAAAsD,IAAA,CAAAR,KAAA,CAAA9C,CAAA,EAAAK,CAAA,YAAAL,CAAA;AAAA,SAAApJ,cAAAwB,CAAA,aAAA6H,CAAA,MAAAA,CAAA,GAAAlI,SAAA,CAAA2H,MAAA,EAAAO,CAAA,UAAAD,CAAA,WAAAjI,SAAA,CAAAkI,CAAA,IAAAlI,SAAA,CAAAkI,CAAA,QAAAA,CAAA,OAAAgD,OAAA,CAAAtC,MAAA,CAAAX,CAAA,OAAAnJ,OAAA,WAAAoJ,CAAA,IAAAnL,eAAA,CAAAsD,CAAA,EAAA6H,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAU,MAAA,CAAA4C,yBAAA,GAAA5C,MAAA,CAAA6C,gBAAA,CAAApL,CAAA,EAAAuI,MAAA,CAAA4C,yBAAA,CAAAvD,CAAA,KAAAiD,OAAA,CAAAtC,MAAA,CAAAX,CAAA,GAAAnJ,OAAA,WAAAoJ,CAAA,IAAAU,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,EAAA6H,CAAA,EAAAU,MAAA,CAAA0C,wBAAA,CAAArD,CAAA,EAAAC,CAAA,iBAAA7H,CAAA;AAAA,SAAAvD,gBAAAqM,CAAA,EAAAf,CAAA,UAAAe,CAAA,YAAAf,CAAA,aAAAmB,SAAA;AAAA,SAAAmC,kBAAArL,CAAA,EAAA6H,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAAP,MAAA,EAAAM,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAAgC,UAAA,GAAAhC,CAAA,CAAAgC,UAAA,QAAAhC,CAAA,CAAAiC,YAAA,kBAAAjC,CAAA,KAAAA,CAAA,CAAAkC,QAAA,QAAA5B,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,EAAAsL,cAAA,CAAArD,CAAA,CAAAnL,GAAA,GAAAmL,CAAA;AAAA,SAAApL,aAAAmD,CAAA,EAAA6H,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAAwD,iBAAA,CAAArL,CAAA,CAAAoI,SAAA,EAAAP,CAAA,GAAAD,CAAA,IAAAyD,iBAAA,CAAArL,CAAA,EAAA4H,CAAA,GAAAW,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,iBAAAmK,QAAA,SAAAnK,CAAA;AAAA,SAAAtD,gBAAAsD,CAAA,EAAA6H,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAAyD,cAAA,CAAAzD,CAAA,MAAA7H,CAAA,GAAAuI,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,EAAA6H,CAAA,IAAA9K,KAAA,EAAA6K,CAAA,EAAAqC,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAAnK,CAAA,CAAA6H,CAAA,IAAAD,CAAA,EAAA5H,CAAA;AAAA,SAAAsL,eAAA1D,CAAA,QAAAP,CAAA,GAAAkE,YAAA,CAAA3D,CAAA,gCAAAnB,OAAA,CAAAY,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAkE,aAAA3D,CAAA,EAAAC,CAAA,oBAAApB,OAAA,CAAAmB,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAA5H,CAAA,GAAA4H,CAAA,CAAAE,MAAA,CAAA0D,WAAA,kBAAAxL,CAAA,QAAAqH,CAAA,GAAArH,CAAA,CAAAmJ,IAAA,CAAAvB,CAAA,EAAAC,CAAA,gCAAApB,OAAA,CAAAY,CAAA,UAAAA,CAAA,YAAA6B,SAAA,yEAAArB,CAAA,GAAA4D,MAAA,GAAAC,MAAA,EAAA9D,CAAA;AADA;AACA;AACA;;AAEkH;AACjE;AACU;AACQ;AAE5D,IAAMiE,UAAU;EAqBrB,SAAAA,WAAA,EAAsB;IAAApP,eAAA,OAAAoP,UAAA;IAAAnP,eAAA,wBAlBiB;MACrCoP,QAAQ,EAAE;QACRC,EAAE,EAAE,sEAAsE;QAC1EC,GAAG,EAAE;MACP,CAAC;MACDC,UAAU,EAAE;QACVF,EAAE,EAAE,uCAAuC;QAC3CC,GAAG,EAAE;MACP,CAAC;MACDE,eAAe,EAAE;QACfH,EAAE,EAAE,wCAAwC;QAC5CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,EAAE,EAAE;MACN;IACF,CAAC;IAGC,IAAI,CAACK,MAAM,GAAGR,yDAAc,CAACS,WAAW,CAAC,CAAC;IAC1C,IAAI,CAACD,MAAM,CAACE,SAAS,CAAC,IAAI,CAACC,aAAa,CAAC;EAC3C;EAAC,OAAA1P,YAAA,CAAAgP,UAAA;IAAA/O,GAAA;IAAAC,KAAA;IASD;AACF;AACA;IACE,SAAOuP,SAASA,CAACE,MAA8B,EAAQ;MACrD,IAAI,CAACD,aAAa,GAAA/N,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAAC+N,aAAa,GAAKC,MAAM,CAAE;MACzD,IAAI,CAACJ,MAAM,CAACE,SAAS,CAAC,IAAI,CAACC,aAAa,CAAC;IAC3C;;IAEA;AACF;AACA;EAFE;IAAAzP,GAAA;IAAAC,KAAA;MAAA,IAAA0P,UAAA,GAAAhC,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAA4C,QAAuBF,MAAiB;QAAA,IAAAhQ,SAAA;QAAA,OAAAoN,YAAA,GAAAC,CAAA,WAAA8C,QAAA;UAAA,kBAAAA,QAAA,CAAA5E,CAAA;YAAA;cACtC,IAAI,CAAC6E,cAAc,CAACJ,MAAM,CAAC;cAErBhQ,SAAS,GAAG,IAAI,CAACqQ,gBAAgB,CAACL,MAAM,CAAChQ,SAAS,CAAC;cAAA,MAErDgQ,MAAM,CAACtP,IAAI,KAAKyO,2CAAO,CAACmB,MAAM;gBAAAH,QAAA,CAAA5E,CAAA;gBAAA;cAAA;cAAA,OAAA4E,QAAA,CAAA7D,CAAA,IACzB,IAAI,CAACiE,WAAW,CAACP,MAAM,EAAiBhQ,SAAS,CAAC;YAAA;cAAA,MAChDgQ,MAAM,CAACtP,IAAI,KAAKyO,2CAAO,CAACqB,MAAM;gBAAAL,QAAA,CAAA5E,CAAA;gBAAA;cAAA;cAAA,OAAA4E,QAAA,CAAA7D,CAAA,IAChC,IAAI,CAACmE,WAAW,CAACT,MAAM,EAAiBhQ,SAAS,CAAC;YAAA;cAAA,OAAAmQ,QAAA,CAAA7D,CAAA,IAGlD,IAAI,CAACoE,iBAAiB,CAACV,MAAM,EAAEhQ,SAAS,CAAC;YAAA;cAAA,OAAAmQ,QAAA,CAAA7D,CAAA;UAAA;QAAA,GAAA4D,OAAA;MAAA,CAEnD;MAAA,SAbYS,SAASA,CAAAC,EAAA;QAAA,OAAAX,UAAA,CAAA/B,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAATwN,SAAS;IAAA;IAetB;AACF;AACA;IAFE;EAAA;IAAArQ,GAAA;IAAAC,KAAA;MAAA,IAAAsQ,kBAAA,GAAA5C,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAAwD,SAAgCd,MAAiB,EAAEhQ,SAAsB;QAAA,IAAA+Q,aAAA,EAAAC,QAAA,EAAAC,QAAA;QAAA,OAAA7D,YAAA,GAAAC,CAAA,WAAA6D,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,CAAA;YAAA;cACvE;cACMwF,aAAa,GAAG,IAAI,CAACI,gBAAgB,CAACnB,MAAM,CAAC;cAAA,KAE/Ce,aAAa;gBAAAG,SAAA,CAAA3F,CAAA;gBAAA;cAAA;cACfpJ,OAAO,CAACiP,GAAG,CAAC,4CAA4C,CAAC;cACnDJ,QAAqB,GAAAhP,aAAA,CAAAA,aAAA,KAAQgO,MAAM;gBAAEtP,IAAI,EAAEyO,2CAAO,CAACqB;cAAM;cAAA,OAAAU,SAAA,CAAA5E,CAAA,IACxD,IAAI,CAACmE,WAAW,CAACO,QAAQ,EAAEhR,SAAS,CAAC;YAAA;cAE5CmC,OAAO,CAACiP,GAAG,CAAC,4CAA4C,CAAC;cACnDH,QAAqB,GAAAjP,aAAA,CAAAA,aAAA,KAAQgO,MAAM;gBAAEtP,IAAI,EAAEyO,2CAAO,CAACmB;cAAM;cAAA,OAAAY,SAAA,CAAA5E,CAAA,IACxD,IAAI,CAACiE,WAAW,CAACU,QAAQ,EAAEjR,SAAS,CAAC;YAAA;cAAA,OAAAkR,SAAA,CAAA5E,CAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA,CAE/C;MAAA,SAbaJ,iBAAiBA,CAAAW,GAAA,EAAAC,GAAA;QAAA,OAAAT,kBAAA,CAAA3C,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAjBuN,iBAAiB;IAAA;IAe/B;AACF;AACA;IAFE;EAAA;IAAApQ,GAAA;IAAAC,KAAA,EAGA,SAAQ4Q,gBAAgBA,CAACnB,MAAW,EAAW;MAC7C;MACA,IAAMuB,WAAW,GAAG,CAAC,EACnBvB,MAAM,CAACwB,WAAW,IAClBxB,MAAM,CAACxF,eAAe,IACtBwF,MAAM,CAACyB,OAAO,IACdzB,MAAM,CAAC0B,KAAK,IACZ1B,MAAM,CAAC2B,MAAM,IACb3B,MAAM,CAAC4B,SAAS,IAChB5B,MAAM,CAACtL,KAAK,GAAG,CAAC,CACjB;;MAED;MACA,IAAMmN,UAAU,GAAG7B,MAAM,CAAChK,KAAK,IAAIiE,OAAA,CAAO+F,MAAM,CAAChK,KAAK,MAAK,QAAQ,IACjEgK,MAAM,CAAChK,KAAK,CAAC+E,MAAM,IACnBiF,MAAM,CAAChK,KAAK,CAAC+E,MAAM,CAAC+G,IAAI,CAAC,UAACvP,KAAU;QAAA,OAClCA,KAAK,CAAC7B,IAAI,KAAK,gBAAgB,IAC/B6B,KAAK,CAAC7B,IAAI,KAAK,WAAW,IAC1B6B,KAAK,CAAC7B,IAAI,KAAK,YAAY;MAAA,CAC7B,CAAC;MAEH,OAAO6Q,WAAW,IAAIM,UAAU;IAClC;;IAEA;AACF;AACA;EAFE;IAAAvR,GAAA;IAAAC,KAAA;MAAA,IAAAwR,YAAA,GAAA9D,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAA0E,SAA0BhC,MAAmB,EAAEhQ,SAAsB;QAAA,IAAAsP,QAAA,EAAA2C,SAAA,EAAAC,WAAA;QAAA,OAAA9E,YAAA,GAAAC,CAAA,WAAA8E,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,CAAA;YAAA;cAAA4G,SAAA,CAAA5G,CAAA;cAAA,OAE7DuC,OAAO,CAACsE,GAAG,CAAC,CAChB,IAAI,CAACxC,MAAM,CAACyC,YAAY,CAAC,CAAC,EAC1B,IAAI,CAACzC,MAAM,CAAC0C,mBAAmB,CAAC,CAAC,CAClC,CAAC;YAAA;cAEIhD,QAAQ,GAAI7H,MAAM,CAAS6H,QAAQ;cAAA,IACpCA,QAAQ;gBAAA6C,SAAA,CAAA5G,CAAA;gBAAA;cAAA;cAAA,MACL,IAAI1L,+CAAW,CAAC,yBAAyB,CAAC;YAAA;cAGlD;cACMoS,SAAS,GAAG,IAAI,CAACM,eAAe,CAACvC,MAAM,CAAC;cACxCkC,WAAW,GAAG,IAAI5C,QAAQ,CAACnP,GAAG,CAAC8R,SAAS,CAAC,EAE/C;cAAAE,SAAA,CAAA5G,CAAA;cAAA,OACM,IAAIuC,OAAO,CAAO,UAACC,OAAO,EAAEyE,MAAM,EAAK;gBAC3CN,WAAW,CAAClR,EAAE,CAAC,MAAM,EAAE;kBAAA,OAAM+M,OAAO,CAAC,CAAC;gBAAA,EAAC;gBACvCmE,WAAW,CAAClR,EAAE,CAAC,OAAO,EAAE,UAACwC,CAAM;kBAAA,IAAAiP,QAAA;kBAAA,OAAKD,MAAM,CAAC,IAAI3S,+CAAW,oBAAAuC,MAAA,CAAoB,EAAAqQ,QAAA,GAAAjP,CAAC,CAACtB,KAAK,cAAAuQ,QAAA,uBAAPA,QAAA,CAAS3P,OAAO,KAAI,eAAe,CAAE,CAAC,CAAC;gBAAA,EAAC;;gBAEtH;gBACA4P,UAAU,CAAC;kBAAA,OAAMF,MAAM,CAAC,IAAI3S,+CAAW,CAAC,kBAAkB,CAAC,CAAC;gBAAA,GAAE,KAAK,CAAC;cACtE,CAAC,CAAC;YAAA;cAAA,OAAAsS,SAAA,CAAA7F,CAAA,IAEK,IAAItJ,mEAAa,CAACkP,WAAW,EAAElS,SAAS,CAAC;UAAA;QAAA,GAAAgS,QAAA;MAAA,CACjD;MAAA,SA1BazB,WAAWA,CAAAoC,GAAA,EAAAC,GAAA;QAAA,OAAAb,YAAA,CAAA7D,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAXoN,WAAW;IAAA;IA4BzB;AACF;AACA;IAFE;EAAA;IAAAjQ,GAAA;IAAAC,KAAA;MAAA,IAAAsS,YAAA,GAAA5E,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAAwF,SAA0B9C,MAAmB,EAAEhQ,SAAsB;QAAA,IAAAqD,KAAA;QAAA,IAAAqE,UAAA,EAAAqL,YAAA,EAAAnI,MAAA;QAAA,OAAAwC,YAAA,GAAAC,CAAA,WAAA2F,SAAA;UAAA,kBAAAA,SAAA,CAAAzH,CAAA;YAAA;cAAAyH,SAAA,CAAAzH,CAAA;cAAA,OAE7DuC,OAAO,CAACsE,GAAG,CAAC,CAChB,IAAI,CAACxC,MAAM,CAACqD,cAAc,CAAC,CAAC,EAC5B,IAAI,CAACrD,MAAM,CAACsD,aAAa,CAAC,CAAC,CAC5B,CAAC;YAAA;cAEIxL,UAAU,GAAID,MAAM,CAASC,UAAU;cAAA,IACxCA,UAAU;gBAAAsL,SAAA,CAAAzH,CAAA;gBAAA;cAAA;cAAA,MACP,IAAI1L,+CAAW,CAAC,2BAA2B,CAAC;YAAA;cAGpD;cACMkT,YAAY,GAAG,IAAI,CAACI,eAAe,CAACnD,MAAM,EAAEhQ,SAAS,CAAC;cACtD4K,MAAM,GAAG,IAAIlD,UAAU,CAAC0L,MAAM,CAACpT,SAAS,EAAE+S,YAAY,CAAC,EAE7D;cAAAC,SAAA,CAAAzH,CAAA;cAAA,OACM,IAAIuC,OAAO,CAAO,UAACC,OAAO,EAAEyE,MAAM,EAAK;gBAC3C,IAAI5H,MAAM,CAACvD,KAAK,EAAE;kBAChB;kBACA,IAAI2I,MAAM,CAACrM,MAAM,EAAE;oBACjB,IAAMoF,WAAW,GAAGrB,UAAU,CAACsB,UAAU,CAACC,WAAW,CACnD+G,MAAM,CAACrM,MAAM,CAAC,CAAC,CAAC,EAChBqM,MAAM,CAACrM,MAAM,CAAC,CAAC,CAAC,EAChBqM,MAAM,CAAC9L,IAAI,GAAGb,KAAI,CAACgQ,YAAY,CAACrD,MAAM,CAAC9L,IAAI,CAAC,GAAG,KACjD,CAAC;oBAED0G,MAAM,CAACtD,MAAM,CAAC4B,OAAO,CAAC;sBACpBH,WAAW,EAAXA,WAAW;sBACXS,WAAW,EAAE;wBACXF,OAAO,EAAE5B,UAAU,CAACU,IAAI,CAACmB,SAAS,CAACyG,MAAM,CAAC1L,OAAO,IAAI,CAAC,CAAC;wBACvDI,KAAK,EAAEgD,UAAU,CAACU,IAAI,CAACmB,SAAS,CAACyG,MAAM,CAACtL,KAAK,IAAI,CAAC,EAAE,CAAC;wBACrD+E,IAAI,EAAE;sBACR;oBACF,CAAC,CAAC;kBACJ;kBAEAsE,OAAO,CAAC,CAAC;gBACX,CAAC,MAAM;kBACLyE,MAAM,CAAC,IAAI3S,+CAAW,CAAC,+BAA+B,CAAC,CAAC;gBAC1D;cACF,CAAC,CAAC;YAAA;cAAA,OAAAmT,SAAA,CAAA1G,CAAA,IAEK,IAAIlF,2EAAiB,CAACwD,MAAM,EAAE5K,SAAS,CAAC;UAAA;QAAA,GAAA8S,QAAA;MAAA,CAChD;MAAA,SA5CarC,WAAWA,CAAA6C,GAAA,EAAAC,GAAA;QAAA,OAAAV,YAAA,CAAA3E,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAXsN,WAAW;IAAA;IA8CzB;AACF;AACA;IAFE;EAAA;IAAAnQ,GAAA;IAAAC,KAAA,EAGA,SAAQgS,eAAeA,CAACvC,MAAmB,EAAO;MAChD,IAAQtP,IAAI,GAAiCsP,MAAM,CAA3CtP,IAAI;QAAEV,SAAS,GAAsBgQ,MAAM,CAArChQ,SAAS;QAAKwT,YAAY,GAAAC,wBAAA,CAAKzD,MAAM,EAAA0D,SAAA;MAEnD,OAAA1R,aAAA;QACEhC,SAAS,EAAE,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGA,SAAS,CAACwC,EAAE,IAAI,KAAK;QAC5EmB,MAAM,EAAEqM,MAAM,CAACrM,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/BO,IAAI,EAAE8L,MAAM,CAAC9L,IAAI,IAAI,CAAC;QACtBI,OAAO,EAAE0L,MAAM,CAAC1L,OAAO,IAAI,CAAC;QAC5BI,KAAK,EAAEsL,MAAM,CAACtL,KAAK,IAAI;MAAC,GACrB8O,YAAY;IAEnB;;IAEA;AACF;AACA;EAFE;IAAAlT,GAAA;IAAAC,KAAA,EAGA,SAAQ4S,eAAeA,CAACnD,MAAmB,EAAEhQ,SAAsB,EAAO;MACxE,IAAQU,IAAI,GAAoDsP,MAAM,CAA9DtP,IAAI;QAAEiD,MAAM,GAA4CqM,MAAM,CAAxDrM,MAAM;QAAEO,IAAI,GAAsC8L,MAAM,CAAhD9L,IAAI;QAAEI,OAAO,GAA6B0L,MAAM,CAA1C1L,OAAO;QAAEI,KAAK,GAAsBsL,MAAM,CAAjCtL,KAAK;QAAKqO,YAAY,GAAAU,wBAAA,CAAKzD,MAAM,EAAA2D,UAAA;MAEtE,OAAA3R,aAAA;QACE4R,SAAS,EAAE5D,MAAM,CAAC4D,SAAS,KAAK,KAAK;QACrCC,QAAQ,EAAE7D,MAAM,CAAC6D,QAAQ,KAAK,KAAK;QACnCC,eAAe,EAAE9D,MAAM,CAAC8D,eAAe,KAAK,KAAK;QACjDC,gBAAgB,EAAE/D,MAAM,CAAC+D,gBAAgB,KAAK,KAAK;QACnDC,QAAQ,EAAEhE,MAAM,CAACgE,QAAQ,KAAK,KAAK;QACnCC,UAAU,EAAEjE,MAAM,CAACiE,UAAU,KAAK,KAAK;QACvCC,eAAe,EAAElE,MAAM,CAACkE,eAAe,KAAK,KAAK;QACjDC,oBAAoB,EAAEnE,MAAM,CAACmE,oBAAoB,KAAK;MAAK,GACxDpB,YAAY;IAEnB;;IAEA;AACF;AACA;EAFE;IAAAzS,GAAA;IAAAC,KAAA,EAGA,SAAQ8S,YAAYA,CAACnP,IAAY,EAAU;MACzC,OAAO,YAAY,GAAGkE,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEnF,IAAI,GAAG,CAAC,CAAC;IAC7C;;IAEA;AACF;AACA;EAFE;IAAA5D,GAAA;IAAAC,KAAA,EAGA,SAAQ8P,gBAAgBA,CAACrQ,SAA+B,EAAe;MACrE,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;QACjC,IAAMoU,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACtU,SAAS,CAAC,IAAIqU,QAAQ,CAACE,aAAa,CAACvU,SAAS,CAAC;QACvF,IAAI,CAACoU,OAAO,EAAE;UACZ,MAAM,IAAIvU,+CAAW,iCAAAuC,MAAA,CAAiCpC,SAAS,CAAE,CAAC;QACpE;QACA,OAAOoU,OAAO;MAChB;MACA,OAAOpU,SAAS;IAClB;;IAEA;AACF;AACA;EAFE;IAAAM,GAAA;IAAAC,KAAA,EAGA,SAAQ6P,cAAcA,CAACJ,MAAiB,EAAQ;MAC9C,IAAI,CAACA,MAAM,EAAE;QACX,MAAM,IAAInQ,+CAAW,CAAC,+BAA+B,CAAC;MACxD;MAEA,IAAI,CAACmQ,MAAM,CAAChQ,SAAS,EAAE;QACrB,MAAM,IAAIH,+CAAW,CAAC,uBAAuB,CAAC;MAChD;MAEA,IAAImQ,MAAM,CAACtP,IAAI,IAAI,CAACqL,MAAM,CAACyI,MAAM,CAACrF,2CAAO,CAAC,CAACsF,QAAQ,CAACzE,MAAM,CAACtP,IAAI,CAAC,EAAE;QAChE,MAAM,IAAIb,+CAAW,sBAAAuC,MAAA,CAAsB4N,MAAM,CAACtP,IAAI,CAAE,CAAC;MAC3D;IACF;;IAEA;AACF;AACA;EAFE;IAAAJ,GAAA;IAAAC,KAAA,EAGA,SAAOmU,eAAeA,CAAA,EAAG;MACvB,OAAO,IAAI,CAAC9E,MAAM,CAAC8E,eAAe,CAAC,CAAC;IACtC;EAAC;IAAApU,GAAA;IAAAC,KAAA,EA5OD,SAAcsP,WAAWA,CAAA,EAAe;MACtC,IAAI,CAACR,UAAU,CAACtP,QAAQ,EAAE;QACxBsP,UAAU,CAACtP,QAAQ,GAAG,IAAIsP,UAAU,CAAC,CAAC;MACxC;MACA,OAAOA,UAAU,CAACtP,QAAQ;IAC5B;EAAC;AAAA;;;;;;;;;;;;;;;;;;;;ACxCH;AACA;AACA;;AAEqD;AAE9C,IAAM4U,QAAQ;EAInB,SAAAA,SAAYzC,WAAyB,EAAE;IAAAjS,eAAA,OAAA0U,QAAA;IACrC,IAAI,CAACzC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC0C,KAAK,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;EAFE,OAAAxU,YAAA,CAAAsU,QAAA;IAAArU,GAAA;IAAAC,KAAA,EAGA,SAAQsU,WAAWA,CAAA,EAAQ;MAAA,IAAAxR,KAAA;MACzB,OAAO,IAAIyR,KAAK,CAAC,IAAI,CAAC5C,WAAW,EAAE;QACjC5Q,GAAG,EAAE,SAALA,GAAGA,CAAGX,MAAoB,EAAEoU,QAAyB,EAAEC,QAAa,EAAK;UACvE;UACA,IAAID,QAAQ,IAAIpU,MAAM,EAAE;YACtB,IAAMJ,KAAK,GAAG0U,OAAO,CAAC3T,GAAG,CAACX,MAAM,EAAEoU,QAAQ,EAAEC,QAAQ,CAAC;;YAErD;YACA,IAAI,OAAOzU,KAAK,KAAK,UAAU,EAAE;cAC/B,OAAOA,KAAK,CAACiM,IAAI,CAAC7L,MAAM,CAAC;YAC3B;YAEA,OAAOJ,KAAK;UACd;;UAEA;UACA,IAAM2U,gBAAgB,GAAGvU,MAAM,CAAC0B,mBAAmB,CAAC,CAAC;UACrD,IAAI6S,gBAAgB,IAAIH,QAAQ,IAAIG,gBAAgB,EAAE;YACpD,IAAM3U,MAAK,GAAG2U,gBAAgB,CAACH,QAAQ,CAAC;;YAExC;YACA,IAAI,OAAOxU,MAAK,KAAK,UAAU,EAAE;cAC/B,OAAOA,MAAK,CAACiM,IAAI,CAAC0I,gBAAgB,CAAC;YACrC;YAEA,OAAO3U,MAAK;UACd;;UAEA;UACA,OAAO8C,KAAI,CAAC8R,uBAAuB,CAACxU,MAAM,EAAEoU,QAAkB,CAAC;QACjE,CAAC;QAED3T,GAAG,EAAE,SAALA,GAAGA,CAAGT,MAAoB,EAAEoU,QAAyB,EAAExU,KAAU,EAAEyU,QAAa,EAAK;UACnF;UACA,IAAID,QAAQ,IAAIpU,MAAM,EAAE;YACtB,OAAOsU,OAAO,CAAC7T,GAAG,CAACT,MAAM,EAAEoU,QAAQ,EAAExU,KAAK,EAAEyU,QAAQ,CAAC;UACvD;;UAEA;UACA,IAAME,gBAAgB,GAAGvU,MAAM,CAAC0B,mBAAmB,CAAC,CAAC;UACrD,IAAI6S,gBAAgB,IAAIH,QAAQ,IAAIG,gBAAgB,EAAE;YACpDA,gBAAgB,CAACH,QAAQ,CAAC,GAAGxU,KAAK;YAClC,OAAO,IAAI;UACb;;UAEA;UACA,MAAM,IAAIV,+CAAW,cAAAuC,MAAA,CAAc6M,MAAM,CAAC8F,QAAQ,CAAC,qCAAkC,CAAC;QACxF,CAAC;QAED5T,GAAG,EAAE,SAALA,GAAGA,CAAGR,MAAoB,EAAEoU,QAAyB,EAAK;UACxD;UACA,IAAIA,QAAQ,IAAIpU,MAAM,EAAE;YACtB,OAAO,IAAI;UACb;;UAEA;UACA,IAAMuU,gBAAgB,GAAGvU,MAAM,CAAC0B,mBAAmB,CAAC,CAAC;UACrD,IAAI6S,gBAAgB,IAAIH,QAAQ,IAAIG,gBAAgB,EAAE;YACpD,OAAO,IAAI;UACb;UAEA,OAAO,KAAK;QACd,CAAC;QAED7G,OAAO,EAAE,SAATA,OAAOA,CAAG1N,MAAoB,EAAK;UACjC,IAAM2N,IAAI,GAAG,IAAIjN,GAAG,CAAkB,CAAC;;UAEvC;UACA0K,MAAM,CAACqJ,mBAAmB,CAACzU,MAAM,CAAC,CAACsB,OAAO,CAAC,UAAA3B,GAAG;YAAA,OAAIgO,IAAI,CAAC/M,GAAG,CAACjB,GAAG,CAAC;UAAA,EAAC;UAChEyL,MAAM,CAACwC,qBAAqB,CAAC5N,MAAM,CAAC,CAACsB,OAAO,CAAC,UAAA3B,GAAG;YAAA,OAAIgO,IAAI,CAAC/M,GAAG,CAACjB,GAAG,CAAC;UAAA,EAAC;;UAElE;UACA,IAAM4U,gBAAgB,GAAGvU,MAAM,CAAC0B,mBAAmB,CAAC,CAAC;UACrD,IAAI6S,gBAAgB,EAAE;YACpBnJ,MAAM,CAACqJ,mBAAmB,CAACF,gBAAgB,CAAC,CAACjT,OAAO,CAAC,UAAA3B,GAAG;cAAA,OAAIgO,IAAI,CAAC/M,GAAG,CAACjB,GAAG,CAAC;YAAA,EAAC;YAC1EyL,MAAM,CAACwC,qBAAqB,CAAC2G,gBAAgB,CAAC,CAACjT,OAAO,CAAC,UAAA3B,GAAG;cAAA,OAAIgO,IAAI,CAAC/M,GAAG,CAACjB,GAAG,CAAC;YAAA,EAAC;UAC9E;UAEA,OAAO+U,KAAK,CAACC,IAAI,CAAChH,IAAI,CAAC;QACzB,CAAC;QAEDG,wBAAwB,EAAE,SAA1BA,wBAAwBA,CAAG9N,MAAoB,EAAEoU,QAAyB,EAAK;UAC7E;UACA,IAAIQ,UAAU,GAAGxJ,MAAM,CAAC0C,wBAAwB,CAAC9N,MAAM,EAAEoU,QAAQ,CAAC;UAClE,IAAIQ,UAAU,EAAE;YACd,OAAOA,UAAU;UACnB;;UAEA;UACA,IAAML,gBAAgB,GAAGvU,MAAM,CAAC0B,mBAAmB,CAAC,CAAC;UACrD,IAAI6S,gBAAgB,EAAE;YACpBK,UAAU,GAAGxJ,MAAM,CAAC0C,wBAAwB,CAACyG,gBAAgB,EAAEH,QAAQ,CAAC;YACxE,IAAIQ,UAAU,EAAE;cACd,OAAOA,UAAU;YACnB;UACF;UAEA,OAAOC,SAAS;QAClB;MACF,CAAC,CAAC;IACJ;;IAEA;AACF;AACA;EAFE;IAAAlV,GAAA;IAAAC,KAAA,EAGA,SAAQ4U,uBAAuBA,CAACxU,MAAoB,EAAEoU,QAAgB,EAAO;MAC3E,IAAMG,gBAAgB,GAAGvU,MAAM,CAAC0B,mBAAmB,CAAC,CAAC;MAErD,QAAQ0S,QAAQ;QACd;QACA,KAAK,KAAK;QACV,KAAK,QAAQ;QACb,KAAK,OAAO;UACV,OAAOG,gBAAgB;;QAEzB;QACA,KAAK,SAAS;UACZ,OAAO,IAAI,CAACO,UAAU,CAACP,gBAAgB,CAAC;;QAE1C;QACA,KAAK,SAAS;UACZ,OAAO,IAAI,CAACQ,UAAU,CAACR,gBAAgB,CAAC;;QAE1C;QACA,KAAK,QAAQ;UACX,OAAO,IAAI,CAACS,QAAQ,CAACT,gBAAgB,CAAC;;QAExC;QACA,KAAK,WAAW;UACd,OAAOvU,MAAM,CAACI,YAAY,CAAC,CAAC;;QAE9B;QACA;UACE,MAAM,IAAIlB,+CAAW,CACnB,uBAAAuC,MAAA,CAAuB2S,QAAQ,gEAAA3S,MAAA,CACT,IAAI,CAACwT,mBAAmB,CAACjV,MAAM,CAAC,CAACkV,IAAI,CAAC,IAAI,CAAC,CACnE,CAAC;MACL;IACF;;IAEA;AACF;AACA;EAFE;IAAAvV,GAAA;IAAAC,KAAA,EAGA,SAAQkV,UAAUA,CAACP,gBAAqB,EAAU;MAChD,IAAIA,gBAAgB,IAAI,OAAOA,gBAAgB,CAACY,OAAO,KAAK,QAAQ,EAAE;QACpE,OAAOZ,gBAAgB,CAACY,OAAO;MACjC;;MAEA;MACA,IAAI,OAAQrO,MAAM,CAAS6H,QAAQ,KAAK,WAAW,EAAE;QACnD,OAAQ7H,MAAM,CAAS6H,QAAQ,CAACwG,OAAO,IAAI,SAAS;MACtD;MAEA,IAAI,OAAQrO,MAAM,CAASC,UAAU,KAAK,WAAW,EAAE;QACrD,OAAQD,MAAM,CAASC,UAAU,CAACqO,OAAO,IAAI,SAAS;MACxD;MAEA,OAAO,SAAS;IAClB;;IAEA;AACF;AACA;EAFE;IAAAzV,GAAA;IAAAC,KAAA,EAGA,SAAQmV,UAAUA,CAACR,gBAAqB,EAAU;MAChD,IAAIA,gBAAgB,EAAE;QACpB;QACA,IAAIA,gBAAgB,CAAChP,QAAQ,IAAIgP,gBAAgB,CAACtQ,QAAQ,EAAE;UAC1D,OAAO,IAAI;QACb;;QAEA;QACA,IAAIsQ,gBAAgB,CAAC7N,KAAK,IAAI6N,gBAAgB,CAAC5N,MAAM,EAAE;UACrD,OAAO,IAAI;QACb;MACF;MAEA,OAAO,SAAS;IAClB;;IAEA;AACF;AACA;EAFE;IAAAhH,GAAA;IAAAC,KAAA,EAGA,SAAQoV,QAAQA,CAACT,gBAAqB,EAAW;MAC/C,IAAIA,gBAAgB,EAAE;QACpB;QACA,IAAI,OAAOA,gBAAgB,CAACc,MAAM,KAAK,UAAU,EAAE;UACjD,OAAOd,gBAAgB,CAACc,MAAM,CAAC,CAAC;QAClC;;QAEA;QACA,IAAId,gBAAgB,CAAC7N,KAAK,EAAE;UAC1B,OAAO,IAAI;QACb;MACF;MAEA,OAAO,KAAK;IACd;;IAEA;AACF;AACA;EAFE;IAAA/G,GAAA;IAAAC,KAAA,EAGA,SAAQqV,mBAAmBA,CAACjV,MAAoB,EAAY;MAC1D,IAAMsV,OAAiB,GAAG,EAAE;;MAE5B;MACA,IAAIC,GAAG,GAAGvV,MAAM;MAChB,OAAOuV,GAAG,IAAIA,GAAG,KAAKnK,MAAM,CAACH,SAAS,EAAE;QACtCG,MAAM,CAACqJ,mBAAmB,CAACc,GAAG,CAAC,CAACjU,OAAO,CAAC,UAAAsI,IAAI,EAAI;UAC9C,IAAI,OAAQ2L,GAAG,CAAS3L,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC0L,OAAO,CAACxB,QAAQ,CAAClK,IAAI,CAAC,EAAE;YACvE0L,OAAO,CAACvH,IAAI,CAACnE,IAAI,CAAC;UACpB;QACF,CAAC,CAAC;QACF2L,GAAG,GAAGnK,MAAM,CAACiB,cAAc,CAACkJ,GAAG,CAAC;MAClC;;MAEA;MACA,IAAMhB,gBAAgB,GAAGvU,MAAM,CAAC0B,mBAAmB,CAAC,CAAC;MACrD,IAAI6S,gBAAgB,EAAE;QACpB,IAAIiB,WAAW,GAAGjB,gBAAgB;QAClC,OAAOiB,WAAW,IAAIA,WAAW,KAAKpK,MAAM,CAACH,SAAS,EAAE;UACtDG,MAAM,CAACqJ,mBAAmB,CAACe,WAAW,CAAC,CAAClU,OAAO,CAAC,UAAAsI,IAAI,EAAI;YACtD,IAAI,OAAO4L,WAAW,CAAC5L,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC0L,OAAO,CAACxB,QAAQ,CAAClK,IAAI,CAAC,EAAE;cACtE0L,OAAO,CAACvH,IAAI,CAACnE,IAAI,CAAC;YACpB;UACF,CAAC,CAAC;UACF4L,WAAW,GAAGpK,MAAM,CAACiB,cAAc,CAACmJ,WAAW,CAAC;QAClD;MACF;MAEA,OAAOF,OAAO,CAACG,IAAI,CAAC,CAAC;IACvB;;IAEA;AACF;AACA;EAFE;IAAA9V,GAAA;IAAAC,KAAA,EAGA,SAAO8V,QAAQA,CAAA,EAAQ;MACrB,OAAO,IAAI,CAACzB,KAAK;IACnB;;IAEA;AACF;AACA;EAFE;IAAAtU,GAAA;IAAAC,KAAA,EAGA,SAAO+V,cAAcA,CAAA,EAAiB;MACpC,OAAO,IAAI,CAACpE,WAAW;IACzB;;IAEA;AACF;AACA;EAFE;IAAA5R,GAAA;IAAAC,KAAA,EAGA,SAAO0K,OAAOA,CAAA,EAAS;MACrB,IAAI,IAAI,CAACiH,WAAW,EAAE;QACpB,IAAI,CAACA,WAAW,CAACrM,MAAM,CAAC,CAAC;MAC3B;MACA,IAAI,CAACqM,WAAW,GAAG,IAAW;MAC9B,IAAI,CAAC0C,KAAK,GAAG,IAAI;IACnB;EAAC;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjRH;AACA;AACA;;AAEA;AACO,IAAKzF,OAAO,0BAAPA,OAAO;EAAPA,OAAO;EAAPA,OAAO;EAAA,OAAPA,OAAO;AAAA;;AAKnB;;AAUA;;AAoCA;;AA8CA;;AAGA;;AAaA;;AAcA;;AAUA;;AAqCA;AACO,IAAMtP,WAAW,0BAAA0W,MAAA;EACtB,SAAA1W,YAAYiD,OAAe,EAAS0T,IAAa,EAAE;IAAA,IAAAnT,KAAA;IAAApD,eAAA,OAAAJ,WAAA;IACjDwD,KAAA,GAAAH,UAAA,OAAArD,WAAA,GAAMiD,OAAO;IAAEO,KAAA,CADmBmT,IAAa,GAAbA,IAAa;IAE/CnT,KAAA,CAAKkH,IAAI,GAAG,aAAa;IAAC,OAAAlH,KAAA;EAC5B;EAACD,SAAA,CAAAvD,WAAA,EAAA0W,MAAA;EAAA,OAAAlW,YAAA,CAAAR,WAAA;AAAA,eAAA4W,gBAAA,CAJ8BC,KAAK;;AAOtC;;AAoBA;;;;;;;;;;;;;;;0BC9MA,uKAAAlT,CAAA,EAAA4H,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAb,EAAAQ,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAZ,CAAA,QAAAc,CAAA,GAAAJ,CAAA,IAAAA,CAAA,CAAAK,SAAA,YAAAC,SAAA,GAAAN,CAAA,GAAAM,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAT,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAZ,CAAA,EAAAc,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAV,CAAA,QAAAtE,CAAA,OAAAiF,CAAA,KAAAD,CAAA,KAAAZ,CAAA,KAAAc,CAAA,EAAA7I,CAAA,EAAA8I,CAAA,EAAAC,CAAA,EAAAL,CAAA,EAAAK,CAAA,CAAAC,IAAA,CAAAhJ,CAAA,MAAA+I,CAAA,WAAAA,EAAAnB,CAAA,EAAAC,CAAA,WAAAR,CAAA,GAAAO,CAAA,EAAAO,CAAA,MAAAG,CAAA,GAAAtI,CAAA,EAAA4I,CAAA,CAAAb,CAAA,GAAAF,CAAA,EAAAiB,CAAA,gBAAAC,EAAAlB,CAAA,EAAAE,CAAA,SAAAI,CAAA,GAAAN,CAAA,EAAAS,CAAA,GAAAP,CAAA,EAAAH,CAAA,OAAAjE,CAAA,IAAA+E,CAAA,KAAAT,CAAA,IAAAL,CAAA,GAAAe,CAAA,CAAArB,MAAA,EAAAM,CAAA,UAAAK,CAAA,EAAAZ,CAAA,GAAAsB,CAAA,CAAAf,CAAA,GAAAmB,CAAA,GAAAH,CAAA,CAAAD,CAAA,EAAAM,CAAA,GAAA5B,CAAA,KAAAQ,CAAA,QAAAI,CAAA,GAAAgB,CAAA,KAAAlB,CAAA,MAAAO,CAAA,GAAAjB,CAAA,EAAAc,CAAA,GAAAd,CAAA,YAAAc,CAAA,WAAAd,CAAA,MAAAA,CAAA,MAAArH,CAAA,IAAAqH,CAAA,OAAA0B,CAAA,MAAAd,CAAA,GAAAJ,CAAA,QAAAkB,CAAA,GAAA1B,CAAA,QAAAc,CAAA,MAAAS,CAAA,CAAAC,CAAA,GAAAd,CAAA,EAAAa,CAAA,CAAAb,CAAA,GAAAV,CAAA,OAAA0B,CAAA,GAAAE,CAAA,KAAAhB,CAAA,GAAAJ,CAAA,QAAAR,CAAA,MAAAU,CAAA,IAAAA,CAAA,GAAAkB,CAAA,MAAA5B,CAAA,MAAAQ,CAAA,EAAAR,CAAA,MAAAU,CAAA,EAAAa,CAAA,CAAAb,CAAA,GAAAkB,CAAA,EAAAd,CAAA,cAAAF,CAAA,IAAAJ,CAAA,aAAAiB,CAAA,QAAAnF,CAAA,OAAAoE,CAAA,qBAAAE,CAAA,EAAAU,CAAA,EAAAM,CAAA,QAAAP,CAAA,YAAAQ,SAAA,uCAAAvF,CAAA,UAAAgF,CAAA,IAAAI,CAAA,CAAAJ,CAAA,EAAAM,CAAA,GAAAd,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAW,CAAA,GAAArB,CAAA,GAAAO,CAAA,OAAAnI,CAAA,GAAAsI,CAAA,MAAA3E,CAAA,KAAA0D,CAAA,KAAAc,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAS,CAAA,CAAAb,CAAA,QAAAgB,CAAA,CAAAZ,CAAA,EAAAG,CAAA,KAAAM,CAAA,CAAAb,CAAA,GAAAO,CAAA,GAAAM,CAAA,CAAAC,CAAA,GAAAP,CAAA,aAAAI,CAAA,MAAArB,CAAA,QAAAc,CAAA,KAAAF,CAAA,YAAAL,CAAA,GAAAP,CAAA,CAAAY,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAAuB,IAAA,CAAA9B,CAAA,EAAAiB,CAAA,UAAAY,SAAA,2CAAAtB,CAAA,CAAAwB,IAAA,SAAAxB,CAAA,EAAAU,CAAA,GAAAV,CAAA,CAAA7K,KAAA,EAAAoL,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAP,CAAA,GAAAP,CAAA,CAAAgC,MAAA,KAAAzB,CAAA,CAAAuB,IAAA,CAAA9B,CAAA,GAAAc,CAAA,SAAAG,CAAA,GAAAY,SAAA,uCAAAjB,CAAA,gBAAAE,CAAA,OAAAd,CAAA,GAAArH,CAAA,cAAA4H,CAAA,IAAAjE,CAAA,GAAAiF,CAAA,CAAAb,CAAA,QAAAO,CAAA,GAAAT,CAAA,CAAAsB,IAAA,CAAApB,CAAA,EAAAa,CAAA,OAAAE,CAAA,kBAAAlB,CAAA,IAAAP,CAAA,GAAArH,CAAA,EAAAmI,CAAA,MAAAG,CAAA,GAAAV,CAAA,cAAAc,CAAA,mBAAA3L,KAAA,EAAA6K,CAAA,EAAAwB,IAAA,EAAAzF,CAAA,SAAAkE,CAAA,EAAAI,CAAA,EAAAZ,CAAA,QAAAiB,CAAA,QAAAQ,CAAA,gBAAAT,UAAA,cAAAiB,kBAAA,cAAAC,2BAAA,KAAA3B,CAAA,GAAAW,MAAA,CAAAiB,cAAA,MAAArB,CAAA,MAAAJ,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAU,mBAAA,CAAAb,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAU,CAAA,GAAAiB,0BAAA,CAAAnB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAA1I,CAAA,WAAAuI,MAAA,CAAAkB,cAAA,GAAAlB,MAAA,CAAAkB,cAAA,CAAAzJ,CAAA,EAAAuJ,0BAAA,KAAAvJ,CAAA,CAAA0J,SAAA,GAAAH,0BAAA,EAAAd,mBAAA,CAAAzI,CAAA,EAAAiI,CAAA,yBAAAjI,CAAA,CAAAoI,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAtI,CAAA,WAAAsJ,iBAAA,CAAAlB,SAAA,GAAAmB,0BAAA,EAAAd,mBAAA,CAAAH,CAAA,iBAAAiB,0BAAA,GAAAd,mBAAA,CAAAc,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAAlB,mBAAA,CAAAc,0BAAA,EAAAtB,CAAA,wBAAAQ,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAL,CAAA,gBAAAQ,mBAAA,CAAAH,CAAA,EAAAP,CAAA,iCAAAU,mBAAA,CAAAH,CAAA,8DAAAsB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAAxC,CAAA,EAAAyC,CAAA,EAAApB,CAAA;AAAA,SAAAD,oBAAAzI,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAP,CAAA,GAAAkB,MAAA,CAAAwB,cAAA,QAAA1C,CAAA,uBAAArH,CAAA,IAAAqH,CAAA,QAAAoB,mBAAA,YAAAuB,mBAAAhK,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAAR,CAAA,GAAAA,CAAA,CAAArH,CAAA,EAAA6H,CAAA,IAAA9K,KAAA,EAAAgL,CAAA,EAAAkC,UAAA,GAAArC,CAAA,EAAAsC,YAAA,GAAAtC,CAAA,EAAAuC,QAAA,GAAAvC,CAAA,MAAA5H,CAAA,CAAA6H,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAU,mBAAA,CAAAzI,CAAA,EAAA6H,CAAA,YAAA7H,CAAA,gBAAAoK,OAAA,CAAAvC,CAAA,EAAAE,CAAA,EAAA/H,CAAA,UAAAiI,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAQ,mBAAA,CAAAzI,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAAyC,mBAAAtC,CAAA,EAAAH,CAAA,EAAA5H,CAAA,EAAA6H,CAAA,EAAAI,CAAA,EAAAa,CAAA,EAAAX,CAAA,cAAAd,CAAA,GAAAU,CAAA,CAAAe,CAAA,EAAAX,CAAA,GAAAG,CAAA,GAAAjB,CAAA,CAAAtK,KAAA,WAAAgL,CAAA,gBAAA/H,CAAA,CAAA+H,CAAA,KAAAV,CAAA,CAAA+B,IAAA,GAAAxB,CAAA,CAAAU,CAAA,IAAAgC,OAAA,CAAAC,OAAA,CAAAjC,CAAA,EAAAkC,IAAA,CAAA3C,CAAA,EAAAI,CAAA;AAAA,SAAAwC,kBAAA1C,CAAA,6BAAAH,CAAA,SAAA5H,CAAA,GAAAL,SAAA,aAAA2K,OAAA,WAAAzC,CAAA,EAAAI,CAAA,QAAAa,CAAA,GAAAf,CAAA,CAAA2C,KAAA,CAAA9C,CAAA,EAAA5H,CAAA,YAAA2K,MAAA5C,CAAA,IAAAsC,kBAAA,CAAAvB,CAAA,EAAAjB,CAAA,EAAAI,CAAA,EAAA0C,KAAA,EAAAC,MAAA,UAAA7C,CAAA,cAAA6C,OAAA7C,CAAA,IAAAsC,kBAAA,CAAAvB,CAAA,EAAAjB,CAAA,EAAAI,CAAA,EAAA0C,KAAA,EAAAC,MAAA,WAAA7C,CAAA,KAAA4C,KAAA;AAAA,SAAAE,QAAA7K,CAAA,EAAA6H,CAAA,QAAAD,CAAA,GAAAW,MAAA,CAAAuC,IAAA,CAAA9K,CAAA,OAAAuI,MAAA,CAAAwC,qBAAA,QAAA9C,CAAA,GAAAM,MAAA,CAAAwC,qBAAA,CAAA/K,CAAA,GAAA6H,CAAA,KAAAI,CAAA,GAAAA,CAAA,CAAA+C,MAAA,WAAAnD,CAAA,WAAAU,MAAA,CAAA0C,wBAAA,CAAAjL,CAAA,EAAA6H,CAAA,EAAAoC,UAAA,OAAArC,CAAA,CAAAsD,IAAA,CAAAR,KAAA,CAAA9C,CAAA,EAAAK,CAAA,YAAAL,CAAA;AAAA,SAAApJ,cAAAwB,CAAA,aAAA6H,CAAA,MAAAA,CAAA,GAAAlI,SAAA,CAAA2H,MAAA,EAAAO,CAAA,UAAAD,CAAA,WAAAjI,SAAA,CAAAkI,CAAA,IAAAlI,SAAA,CAAAkI,CAAA,QAAAA,CAAA,OAAAgD,OAAA,CAAAtC,MAAA,CAAAX,CAAA,OAAAnJ,OAAA,WAAAoJ,CAAA,IAAAnL,eAAA,CAAAsD,CAAA,EAAA6H,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAU,MAAA,CAAA4C,yBAAA,GAAA5C,MAAA,CAAA6C,gBAAA,CAAApL,CAAA,EAAAuI,MAAA,CAAA4C,yBAAA,CAAAvD,CAAA,KAAAiD,OAAA,CAAAtC,MAAA,CAAAX,CAAA,GAAAnJ,OAAA,WAAAoJ,CAAA,IAAAU,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,EAAA6H,CAAA,EAAAU,MAAA,CAAA0C,wBAAA,CAAArD,CAAA,EAAAC,CAAA,iBAAA7H,CAAA;AAAA,SAAAvD,gBAAAqM,CAAA,EAAAf,CAAA,UAAAe,CAAA,YAAAf,CAAA,aAAAmB,SAAA;AAAA,SAAAmC,kBAAArL,CAAA,EAAA6H,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAAP,MAAA,EAAAM,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAAgC,UAAA,GAAAhC,CAAA,CAAAgC,UAAA,QAAAhC,CAAA,CAAAiC,YAAA,kBAAAjC,CAAA,KAAAA,CAAA,CAAAkC,QAAA,QAAA5B,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,EAAAsL,cAAA,CAAArD,CAAA,CAAAnL,GAAA,GAAAmL,CAAA;AAAA,SAAApL,aAAAmD,CAAA,EAAA6H,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAAwD,iBAAA,CAAArL,CAAA,CAAAoI,SAAA,EAAAP,CAAA,GAAAD,CAAA,IAAAyD,iBAAA,CAAArL,CAAA,EAAA4H,CAAA,GAAAW,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,iBAAAmK,QAAA,SAAAnK,CAAA;AAAA,SAAAtD,gBAAAsD,CAAA,EAAA6H,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAAyD,cAAA,CAAAzD,CAAA,MAAA7H,CAAA,GAAAuI,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,EAAA6H,CAAA,IAAA9K,KAAA,EAAA6K,CAAA,EAAAqC,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAAnK,CAAA,CAAA6H,CAAA,IAAAD,CAAA,EAAA5H,CAAA;AAAA,SAAAsL,eAAA1D,CAAA,QAAAP,CAAA,GAAAkE,YAAA,CAAA3D,CAAA,gCAAAnB,OAAA,CAAAY,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAkE,aAAA3D,CAAA,EAAAC,CAAA,oBAAApB,OAAA,CAAAmB,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAA5H,CAAA,GAAA4H,CAAA,CAAAE,MAAA,CAAA0D,WAAA,kBAAAxL,CAAA,QAAAqH,CAAA,GAAArH,CAAA,CAAAmJ,IAAA,CAAAvB,CAAA,EAAAC,CAAA,gCAAApB,OAAA,CAAAY,CAAA,UAAAA,CAAA,YAAA6B,SAAA,yEAAArB,CAAA,GAAA4D,MAAA,GAAAC,MAAA,EAAA9D,CAAA;AADA;AACA;AACA;;AAEoE;AAE7D,IAAMgE,cAAc;EAMzB,SAAAA,eAAA,EAAsB;IAAAnP,eAAA,OAAAmP,cAAA;IAAAlP,eAAA,uBAJe,CAAC,CAAC;IAAAA,eAAA,0BACe,IAAIC,GAAG,CAAC,CAAC;IAAAD,eAAA,iBAC/B,CAAC,CAAC;EAEX;EAAC,OAAAG,YAAA,CAAA+O,cAAA;IAAA9O,GAAA;IAAAC,KAAA;IASxB;AACF;AACA;IACE,SAAOuP,SAASA,CAACE,MAAqB,EAAQ;MAC5C,IAAI,CAACA,MAAM,GAAAhO,aAAA,CAAAA,aAAA,KAAQ,IAAI,CAACgO,MAAM,GAAKA,MAAM,CAAE;IAC7C;;IAEA;AACF;AACA;EAFE;IAAA1P,GAAA;IAAAC,KAAA,EAGA,SAAQoW,OAAOA,CAACzM,GAAW,EAAiB;MAC1C,OAAO,IAAI4D,OAAO,CAAC,UAACC,OAAO,EAAEyE,MAAM,EAAK;QACtC;QACA,IAAMoE,YAAY,GAAGvC,QAAQ,CAACE,aAAa,gBAAAnS,MAAA,CAAe8H,GAAG,QAAI,CAAC;QAClE,IAAI0M,YAAY,EAAE;UAChB7I,OAAO,CAAC,CAAC;UACT;QACF;QAEA,IAAM8I,IAAI,GAAGxC,QAAQ,CAACyC,aAAa,CAAC,MAAM,CAAC;QAC3CD,IAAI,CAACE,GAAG,GAAG,YAAY;QACvBF,IAAI,CAACG,IAAI,GAAG9M,GAAG;QAEf2M,IAAI,CAACI,MAAM,GAAG;UAAA,OAAMlJ,OAAO,CAAC,CAAC;QAAA;QAC7B8I,IAAI,CAACK,OAAO,GAAG;UAAA,OAAM1E,MAAM,CAAC,IAAI3S,+CAAW,wBAAAuC,MAAA,CAAwB8H,GAAG,CAAE,CAAC,CAAC;QAAA;QAE1EmK,QAAQ,CAAC8C,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MACjC,CAAC,CAAC;IACJ;;IAEA;AACF;AACA;EAFE;IAAAvW,GAAA;IAAAC,KAAA,EAGA,SAAQ8W,MAAMA,CAACnN,GAAW,EAAiB;MACzC,OAAO,IAAI4D,OAAO,CAAC,UAACC,OAAO,EAAEyE,MAAM,EAAK;QACtC;QACA,IAAM8E,cAAc,GAAGjD,QAAQ,CAACE,aAAa,iBAAAnS,MAAA,CAAgB8H,GAAG,QAAI,CAAC;QACrE,IAAIoN,cAAc,EAAE;UAClBvJ,OAAO,CAAC,CAAC;UACT;QACF;QAEA,IAAMwJ,MAAM,GAAGlD,QAAQ,CAACyC,aAAa,CAAC,QAAQ,CAAC;QAC/CS,MAAM,CAACC,GAAG,GAAGtN,GAAG;QAChBqN,MAAM,CAAC7W,IAAI,GAAG,iBAAiB;QAE/B6W,MAAM,CAACN,MAAM,GAAG;UAAA,OAAMlJ,OAAO,CAAC,CAAC;QAAA;QAC/BwJ,MAAM,CAACL,OAAO,GAAG;UAAA,OAAM1E,MAAM,CAAC,IAAI3S,+CAAW,uBAAAuC,MAAA,CAAuB8H,GAAG,CAAE,CAAC,CAAC;QAAA;QAE3EmK,QAAQ,CAAC8C,IAAI,CAACC,WAAW,CAACG,MAAM,CAAC;MACnC,CAAC,CAAC;IACJ;;IAEA;AACF;AACA;EAFE;IAAAjX,GAAA;IAAAC,KAAA;MAAA,IAAAkX,cAAA,GAAAxJ,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAA4C,QAAA;QAAA,IAAAwH,QAAA,EAAAC,WAAA,EAAAC,EAAA;QAAA,OAAAxK,YAAA,GAAAC,CAAA,WAAA8C,QAAA;UAAA,kBAAAA,QAAA,CAAA5E,CAAA;YAAA;cAAA,KACM,IAAI,CAACsM,YAAY,CAACvI,QAAQ;gBAAAa,QAAA,CAAA5E,CAAA;gBAAA;cAAA;cAAA,OAAA4E,QAAA,CAAA7D,CAAA;YAAA;cAIxBoL,QAAQ,GAAG,UAAU;cAAA,KACvB,IAAI,CAACI,eAAe,CAAC3W,GAAG,CAACuW,QAAQ,CAAC;gBAAAvH,QAAA,CAAA5E,CAAA;gBAAA;cAAA;cAAA,OAAA4E,QAAA,CAAA7D,CAAA,IAC7B,IAAI,CAACwL,eAAe,CAACxW,GAAG,CAACoW,QAAQ,CAAC;YAAA;cAGrCC,WAAW,GAAG,IAAI,CAACI,aAAa,CAAC,CAAC;cACxC,IAAI,CAACD,eAAe,CAAC1W,GAAG,CAACsW,QAAQ,EAAEC,WAAW,CAAC;cAACxH,QAAA,CAAAhE,CAAA;cAAAgE,QAAA,CAAA5E,CAAA;cAAA,OAGxCoM,WAAW;YAAA;cACjB,IAAI,CAACE,YAAY,CAACvI,QAAQ,GAAG,IAAI;cAACa,QAAA,CAAA5E,CAAA;cAAA;YAAA;cAAA4E,QAAA,CAAAhE,CAAA;cAAAyL,EAAA,GAAAzH,QAAA,CAAA9D,CAAA;cAElC,IAAI,CAACyL,eAAe,CAACpW,MAAM,CAACgW,QAAQ,CAAC;cAAC,MAAAE,EAAA;YAAA;cAAA,OAAAzH,QAAA,CAAA7D,CAAA;UAAA;QAAA,GAAA4D,OAAA;MAAA,CAGzC;MAAA,SApBYmC,YAAYA,CAAA;QAAA,OAAAoF,cAAA,CAAAvJ,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAZkP,YAAY;IAAA;EAAA;IAAA/R,GAAA;IAAAC,KAAA;MAAA,IAAAyX,cAAA,GAAA/J,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAsBzB,SAAAwD,SAAA;QAAA,IAAAd,MAAA;QAAA,OAAA5C,YAAA,GAAAC,CAAA,WAAA6D,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,CAAA;YAAA;cACQyE,MAAM,GAAG,IAAI,CAACA,MAAM,CAACV,QAAQ;cAAA,IAC9BU,MAAM;gBAAAkB,SAAA,CAAA3F,CAAA;gBAAA;cAAA;cAAA,MACH,IAAI1L,+CAAW,CAAC,kCAAkC,CAAC;YAAA;cAAA,MAIvD,OAAQ4H,MAAM,CAAS6H,QAAQ,KAAK,WAAW;gBAAA4B,SAAA,CAAA3F,CAAA;gBAAA;cAAA;cAAA,OAAA2F,SAAA,CAAA5E,CAAA;YAAA;cAAA4E,SAAA,CAAA3F,CAAA;cAAA,OAK7CuC,OAAO,CAACsE,GAAG,CAAC,CAChBpC,MAAM,CAACR,GAAG,GAAG,IAAI,CAACmH,OAAO,CAAC3G,MAAM,CAACR,GAAG,CAAC,GAAG1B,OAAO,CAACC,OAAO,CAAC,CAAC,EACzD,IAAI,CAACsJ,MAAM,CAACrH,MAAM,CAACT,EAAE,CAAC,CACvB,CAAC;YAAA;cAAA,MAGE,OAAQ9H,MAAM,CAAS6H,QAAQ,KAAK,WAAW;gBAAA4B,SAAA,CAAA3F,CAAA;gBAAA;cAAA;cAAA,MAC3C,IAAI1L,+CAAW,CAAC,kCAAkC,CAAC;YAAA;cAAA,OAAAqR,SAAA,CAAA5E,CAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA,CAE5D;MAAA,SArBaiH,aAAaA,CAAA;QAAA,OAAAC,cAAA,CAAA9J,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAb4U,aAAa;IAAA;IAuB3B;AACF;AACA;EAFE;IAAAzX,GAAA;IAAAC,KAAA;MAAA,IAAA0X,gBAAA,GAAAhK,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAA0E,SAAA;QAAA,IAAA0F,QAAA,EAAAC,WAAA,EAAAO,GAAA;QAAA,OAAA9K,YAAA,GAAAC,CAAA,WAAA8E,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,CAAA;YAAA;cAAA,KACM,IAAI,CAACsM,YAAY,CAACpI,UAAU;gBAAA0C,SAAA,CAAA5G,CAAA;gBAAA;cAAA;cAAA,OAAA4G,SAAA,CAAA7F,CAAA;YAAA;cAI1BoL,QAAQ,GAAG,YAAY;cAAA,KACzB,IAAI,CAACI,eAAe,CAAC3W,GAAG,CAACuW,QAAQ,CAAC;gBAAAvF,SAAA,CAAA5G,CAAA;gBAAA;cAAA;cAAA,OAAA4G,SAAA,CAAA7F,CAAA,IAC7B,IAAI,CAACwL,eAAe,CAACxW,GAAG,CAACoW,QAAQ,CAAC;YAAA;cAGrCC,WAAW,GAAG,IAAI,CAACQ,eAAe,CAAC,CAAC;cAC1C,IAAI,CAACL,eAAe,CAAC1W,GAAG,CAACsW,QAAQ,EAAEC,WAAW,CAAC;cAACxF,SAAA,CAAAhG,CAAA;cAAAgG,SAAA,CAAA5G,CAAA;cAAA,OAGxCoM,WAAW;YAAA;cACjB,IAAI,CAACE,YAAY,CAACpI,UAAU,GAAG,IAAI;cAAC0C,SAAA,CAAA5G,CAAA;cAAA;YAAA;cAAA4G,SAAA,CAAAhG,CAAA;cAAA+L,GAAA,GAAA/F,SAAA,CAAA9F,CAAA;cAEpC,IAAI,CAACyL,eAAe,CAACpW,MAAM,CAACgW,QAAQ,CAAC;cAAC,MAAAQ,GAAA;YAAA;cAAA,OAAA/F,SAAA,CAAA7F,CAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA,CAGzC;MAAA,SApBYiB,cAAcA,CAAA;QAAA,OAAAgF,gBAAA,CAAA/J,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAd8P,cAAc;IAAA;EAAA;IAAA3S,GAAA;IAAAC,KAAA;MAAA,IAAA6X,gBAAA,GAAAnK,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAsB3B,SAAAwF,SAAA;QAAA,IAAA9C,MAAA,EAAAqI,SAAA;QAAA,OAAAjL,YAAA,GAAAC,CAAA,WAAA2F,SAAA;UAAA,kBAAAA,SAAA,CAAAzH,CAAA;YAAA;cACQyE,MAAM,GAAG,IAAI,CAACA,MAAM,CAACP,UAAU;cAAA,IAChCO,MAAM;gBAAAgD,SAAA,CAAAzH,CAAA;gBAAA;cAAA;cAAA,MACH,IAAI1L,+CAAW,CAAC,oCAAoC,CAAC;YAAA;cAAA,MAIzD,OAAQ4H,MAAM,CAASC,UAAU,KAAK,WAAW;gBAAAsL,SAAA,CAAAzH,CAAA;gBAAA;cAAA;cAAA,OAAAyH,SAAA,CAAA1G,CAAA;YAAA;cAIrD;cACM+L,SAAS,GAAG,CAAC,IAAI,CAAChB,MAAM,CAACrH,MAAM,CAACT,EAAE,CAAC,CAAC;cAC1C,IAAIS,MAAM,CAACR,GAAG,EAAE;gBACd6I,SAAS,CAAC3J,IAAI,CAAC,IAAI,CAACiI,OAAO,CAAC3G,MAAM,CAACR,GAAG,CAAC,CAAC;cAC1C;cAACwD,SAAA,CAAAzH,CAAA;cAAA,OAEKuC,OAAO,CAACsE,GAAG,CAACiG,SAAS,CAAC;YAAA;cAAA,MAGxB,OAAQ5Q,MAAM,CAASC,UAAU,KAAK,WAAW;gBAAAsL,SAAA,CAAAzH,CAAA;gBAAA;cAAA;cAAA,MAC7C,IAAI1L,+CAAW,CAAC,oCAAoC,CAAC;YAAA;cAAA,OAAAmT,SAAA,CAAA1G,CAAA;UAAA;QAAA,GAAAwG,QAAA;MAAA,CAE9D;MAAA,SAvBaqF,eAAeA,CAAA;QAAA,OAAAC,gBAAA,CAAAlK,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAfgV,eAAe;IAAA;IAyB7B;AACF;AACA;EAFE;IAAA7X,GAAA;IAAAC,KAAA;MAAA,IAAA+X,qBAAA,GAAArK,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAAiL,SAAA;QAAA,IAAAb,QAAA,EAAAC,WAAA,EAAAa,GAAA;QAAA,OAAApL,YAAA,GAAAC,CAAA,WAAAoL,SAAA;UAAA,kBAAAA,SAAA,CAAAlN,CAAA;YAAA;cAAA,KACM,IAAI,CAACsM,YAAY,CAACnI,eAAe;gBAAA+I,SAAA,CAAAlN,CAAA;gBAAA;cAAA;cAAA,OAAAkN,SAAA,CAAAnM,CAAA;YAAA;cAI/BoL,QAAQ,GAAG,iBAAiB;cAAA,KAC9B,IAAI,CAACI,eAAe,CAAC3W,GAAG,CAACuW,QAAQ,CAAC;gBAAAe,SAAA,CAAAlN,CAAA;gBAAA;cAAA;cAAA,OAAAkN,SAAA,CAAAnM,CAAA,IAC7B,IAAI,CAACwL,eAAe,CAACxW,GAAG,CAACoW,QAAQ,CAAC;YAAA;cAGrCC,WAAW,GAAG,IAAI,CAACe,oBAAoB,CAAC,CAAC;cAC/C,IAAI,CAACZ,eAAe,CAAC1W,GAAG,CAACsW,QAAQ,EAAEC,WAAW,CAAC;cAACc,SAAA,CAAAtM,CAAA;cAAAsM,SAAA,CAAAlN,CAAA;cAAA,OAGxCoM,WAAW;YAAA;cACjB,IAAI,CAACE,YAAY,CAACnI,eAAe,GAAG,IAAI;cAAC+I,SAAA,CAAAlN,CAAA;cAAA;YAAA;cAAAkN,SAAA,CAAAtM,CAAA;cAAAqM,GAAA,GAAAC,SAAA,CAAApM,CAAA;cAEzC,IAAI,CAACyL,eAAe,CAACpW,MAAM,CAACgW,QAAQ,CAAC;cAAC,MAAAc,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAnM,CAAA;UAAA;QAAA,GAAAiM,QAAA;MAAA,CAGzC;MAAA,SApBYjG,mBAAmBA,CAAA;QAAA,OAAAgG,qBAAA,CAAApK,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAnBmP,mBAAmB;IAAA;EAAA;IAAAhS,GAAA;IAAAC,KAAA;MAAA,IAAAoY,qBAAA,GAAA1K,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAsBhC,SAAAsL,SAAA;QAAA,IAAA5I,MAAA,EAAAV,QAAA;QAAA,OAAAlC,YAAA,GAAAC,CAAA,WAAAwL,SAAA;UAAA,kBAAAA,SAAA,CAAAtN,CAAA;YAAA;cACQyE,MAAM,GAAG,IAAI,CAACA,MAAM,CAACN,eAAe;cAAA,IACrCM,MAAM;gBAAA6I,SAAA,CAAAtN,CAAA;gBAAA;cAAA;cAAA,MACH,IAAI1L,+CAAW,CAAC,0CAA0C,CAAC;YAAA;cAAAgZ,SAAA,CAAAtN,CAAA;cAAA,OAI7D,IAAI,CAAC8G,YAAY,CAAC,CAAC;YAAA;cAEzB;cACM/C,QAAQ,GAAI7H,MAAM,CAAS6H,QAAQ;cAAA,MACrCA,QAAQ,IAAIA,QAAQ,CAACwJ,QAAQ;gBAAAD,SAAA,CAAAtN,CAAA;gBAAA;cAAA;cAAA,OAAAsN,SAAA,CAAAvM,CAAA;YAAA;cAAAuM,SAAA,CAAAtN,CAAA;cAAA,OAK3BuC,OAAO,CAACsE,GAAG,CAAC,CAChB,IAAI,CAACuE,OAAO,CAAC3G,MAAM,CAACR,GAAG,CAAC,EACxB,IAAI,CAAC6H,MAAM,CAACrH,MAAM,CAACT,EAAE,CAAC,CACvB,CAAC;YAAA;cAAA,MAGE,CAACD,QAAQ,IAAI,CAACA,QAAQ,CAACwJ,QAAQ;gBAAAD,SAAA,CAAAtN,CAAA;gBAAA;cAAA;cAAA,MAC3B,IAAI1L,+CAAW,CAAC,8CAA8C,CAAC;YAAA;cAAA,OAAAgZ,SAAA,CAAAvM,CAAA;UAAA;QAAA,GAAAsM,QAAA;MAAA,CAExE;MAAA,SAzBaF,oBAAoBA,CAAA;QAAA,OAAAC,qBAAA,CAAAzK,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAApBuV,oBAAoB;IAAA;IA2BlC;AACF;AACA;EAFE;IAAApY,GAAA;IAAAC,KAAA;MAAA,IAAAwY,eAAA,GAAA9K,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAA0L,SAAA;QAAA,IAAAtB,QAAA,EAAAC,WAAA,EAAAsB,GAAA;QAAA,OAAA7L,YAAA,GAAAC,CAAA,WAAA6L,SAAA;UAAA,kBAAAA,SAAA,CAAA3N,CAAA;YAAA;cAAA,KACM,IAAI,CAACsM,YAAY,CAAClI,SAAS;gBAAAuJ,SAAA,CAAA3N,CAAA;gBAAA;cAAA;cAAA,OAAA2N,SAAA,CAAA5M,CAAA;YAAA;cAIzBoL,QAAQ,GAAG,WAAW;cAAA,KACxB,IAAI,CAACI,eAAe,CAAC3W,GAAG,CAACuW,QAAQ,CAAC;gBAAAwB,SAAA,CAAA3N,CAAA;gBAAA;cAAA;cAAA,OAAA2N,SAAA,CAAA5M,CAAA,IAC7B,IAAI,CAACwL,eAAe,CAACxW,GAAG,CAACoW,QAAQ,CAAC;YAAA;cAGrCC,WAAW,GAAG,IAAI,CAACwB,cAAc,CAAC,CAAC;cACzC,IAAI,CAACrB,eAAe,CAAC1W,GAAG,CAACsW,QAAQ,EAAEC,WAAW,CAAC;cAACuB,SAAA,CAAA/M,CAAA;cAAA+M,SAAA,CAAA3N,CAAA;cAAA,OAGxCoM,WAAW;YAAA;cACjB,IAAI,CAACE,YAAY,CAAClI,SAAS,GAAG,IAAI;cAACuJ,SAAA,CAAA3N,CAAA;cAAA;YAAA;cAAA2N,SAAA,CAAA/M,CAAA;cAAA8M,GAAA,GAAAC,SAAA,CAAA7M,CAAA;cAEnC,IAAI,CAACyL,eAAe,CAACpW,MAAM,CAACgW,QAAQ,CAAC;cAAC,MAAAuB,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA5M,CAAA;UAAA;QAAA,GAAA0M,QAAA;MAAA,CAGzC;MAAA,SApBY9F,aAAaA,CAAA;QAAA,OAAA6F,eAAA,CAAA7K,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAb+P,aAAa;IAAA;EAAA;IAAA5S,GAAA;IAAAC,KAAA;MAAA,IAAA6Y,eAAA,GAAAnL,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAsB1B,SAAA+L,SAAA;QAAA,IAAArJ,MAAA,EAAAtI,UAAA;QAAA,OAAA0F,YAAA,GAAAC,CAAA,WAAAiM,SAAA;UAAA,kBAAAA,SAAA,CAAA/N,CAAA;YAAA;cACQyE,MAAM,GAAG,IAAI,CAACA,MAAM,CAACL,SAAS;cAAA,IAC/BK,MAAM;gBAAAsJ,SAAA,CAAA/N,CAAA;gBAAA;cAAA;cAAA,MACH,IAAI1L,+CAAW,CAAC,mCAAmC,CAAC;YAAA;cAAAyZ,SAAA,CAAA/N,CAAA;cAAA,OAItD,IAAI,CAAC0H,cAAc,CAAC,CAAC;YAAA;cAE3B;cACMvL,UAAU,GAAID,MAAM,CAASC,UAAU;cAAA,MACzCA,UAAU,IAAIA,UAAU,CAAC6R,OAAO;gBAAAD,SAAA,CAAA/N,CAAA;gBAAA;cAAA;cAAA,OAAA+N,SAAA,CAAAhN,CAAA;YAAA;cAAAgN,SAAA,CAAA/N,CAAA;cAAA,OAK9B,IAAI,CAAC8L,MAAM,CAACrH,MAAM,CAACT,EAAE,CAAC;YAAA;cAE5B;cACA,IAAI,CAAC7H,UAAU,IAAI,CAACA,UAAU,CAAC6R,OAAO,EAAE;gBACtCpX,OAAO,CAAC4H,IAAI,CAAC,oEAAoE,CAAC;cACpF;YAAC;cAAA,OAAAuP,SAAA,CAAAhN,CAAA;UAAA;QAAA,GAAA+M,QAAA;MAAA,CACF;MAAA,SAtBaF,cAAcA,CAAA;QAAA,OAAAC,eAAA,CAAAlL,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAdgW,cAAc;IAAA;IAwB5B;AACF;AACA;EAFE;IAAA7Y,GAAA;IAAAC,KAAA,EAGA,SAAOmU,eAAeA,CAAA,EAAiB;MACrC,OAAA1S,aAAA,KAAY,IAAI,CAAC6V,YAAY;IAC/B;;IAEA;AACF;AACA;EAFE;IAAAvX,GAAA;IAAAC,KAAA,EAGA,SAAOiZ,KAAKA,CAAA,EAAS;MACnB,IAAI,CAAC3B,YAAY,GAAG,CAAC,CAAC;MACtB,IAAI,CAACC,eAAe,CAACnW,KAAK,CAAC,CAAC;IAC9B;EAAC;IAAArB,GAAA;IAAAC,KAAA,EAhRD,SAAcsP,WAAWA,CAAA,EAAmB;MAC1C,IAAI,CAACT,cAAc,CAACrP,QAAQ,EAAE;QAC5BqP,cAAc,CAACrP,QAAQ,GAAG,IAAIqP,cAAc,CAAC,CAAC;MAChD;MACA,OAAOA,cAAc,CAACrP,QAAQ;IAChC;EAAC;AAAA;;;;;;UCnBH;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;;;;;;;;;;;;;;;;;0BCCA,uKAAAyD,CAAA,EAAA4H,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAb,EAAAQ,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAZ,CAAA,QAAAc,CAAA,GAAAJ,CAAA,IAAAA,CAAA,CAAAK,SAAA,YAAAC,SAAA,GAAAN,CAAA,GAAAM,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAT,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAZ,CAAA,EAAAc,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAV,CAAA,QAAAtE,CAAA,OAAAiF,CAAA,KAAAD,CAAA,KAAAZ,CAAA,KAAAc,CAAA,EAAA7I,CAAA,EAAA8I,CAAA,EAAAC,CAAA,EAAAL,CAAA,EAAAK,CAAA,CAAAC,IAAA,CAAAhJ,CAAA,MAAA+I,CAAA,WAAAA,EAAAnB,CAAA,EAAAC,CAAA,WAAAR,CAAA,GAAAO,CAAA,EAAAO,CAAA,MAAAG,CAAA,GAAAtI,CAAA,EAAA4I,CAAA,CAAAb,CAAA,GAAAF,CAAA,EAAAiB,CAAA,gBAAAC,EAAAlB,CAAA,EAAAE,CAAA,SAAAI,CAAA,GAAAN,CAAA,EAAAS,CAAA,GAAAP,CAAA,EAAAH,CAAA,OAAAjE,CAAA,IAAA+E,CAAA,KAAAT,CAAA,IAAAL,CAAA,GAAAe,CAAA,CAAArB,MAAA,EAAAM,CAAA,UAAAK,CAAA,EAAAZ,CAAA,GAAAsB,CAAA,CAAAf,CAAA,GAAAmB,CAAA,GAAAH,CAAA,CAAAD,CAAA,EAAAM,CAAA,GAAA5B,CAAA,KAAAQ,CAAA,QAAAI,CAAA,GAAAgB,CAAA,KAAAlB,CAAA,MAAAO,CAAA,GAAAjB,CAAA,EAAAc,CAAA,GAAAd,CAAA,YAAAc,CAAA,WAAAd,CAAA,MAAAA,CAAA,MAAArH,CAAA,IAAAqH,CAAA,OAAA0B,CAAA,MAAAd,CAAA,GAAAJ,CAAA,QAAAkB,CAAA,GAAA1B,CAAA,QAAAc,CAAA,MAAAS,CAAA,CAAAC,CAAA,GAAAd,CAAA,EAAAa,CAAA,CAAAb,CAAA,GAAAV,CAAA,OAAA0B,CAAA,GAAAE,CAAA,KAAAhB,CAAA,GAAAJ,CAAA,QAAAR,CAAA,MAAAU,CAAA,IAAAA,CAAA,GAAAkB,CAAA,MAAA5B,CAAA,MAAAQ,CAAA,EAAAR,CAAA,MAAAU,CAAA,EAAAa,CAAA,CAAAb,CAAA,GAAAkB,CAAA,EAAAd,CAAA,cAAAF,CAAA,IAAAJ,CAAA,aAAAiB,CAAA,QAAAnF,CAAA,OAAAoE,CAAA,qBAAAE,CAAA,EAAAU,CAAA,EAAAM,CAAA,QAAAP,CAAA,YAAAQ,SAAA,uCAAAvF,CAAA,UAAAgF,CAAA,IAAAI,CAAA,CAAAJ,CAAA,EAAAM,CAAA,GAAAd,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAW,CAAA,GAAArB,CAAA,GAAAO,CAAA,OAAAnI,CAAA,GAAAsI,CAAA,MAAA3E,CAAA,KAAA0D,CAAA,KAAAc,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAS,CAAA,CAAAb,CAAA,QAAAgB,CAAA,CAAAZ,CAAA,EAAAG,CAAA,KAAAM,CAAA,CAAAb,CAAA,GAAAO,CAAA,GAAAM,CAAA,CAAAC,CAAA,GAAAP,CAAA,aAAAI,CAAA,MAAArB,CAAA,QAAAc,CAAA,KAAAF,CAAA,YAAAL,CAAA,GAAAP,CAAA,CAAAY,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAAuB,IAAA,CAAA9B,CAAA,EAAAiB,CAAA,UAAAY,SAAA,2CAAAtB,CAAA,CAAAwB,IAAA,SAAAxB,CAAA,EAAAU,CAAA,GAAAV,CAAA,CAAA7K,KAAA,EAAAoL,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAP,CAAA,GAAAP,CAAA,CAAAgC,MAAA,KAAAzB,CAAA,CAAAuB,IAAA,CAAA9B,CAAA,GAAAc,CAAA,SAAAG,CAAA,GAAAY,SAAA,uCAAAjB,CAAA,gBAAAE,CAAA,OAAAd,CAAA,GAAArH,CAAA,cAAA4H,CAAA,IAAAjE,CAAA,GAAAiF,CAAA,CAAAb,CAAA,QAAAO,CAAA,GAAAT,CAAA,CAAAsB,IAAA,CAAApB,CAAA,EAAAa,CAAA,OAAAE,CAAA,kBAAAlB,CAAA,IAAAP,CAAA,GAAArH,CAAA,EAAAmI,CAAA,MAAAG,CAAA,GAAAV,CAAA,cAAAc,CAAA,mBAAA3L,KAAA,EAAA6K,CAAA,EAAAwB,IAAA,EAAAzF,CAAA,SAAAkE,CAAA,EAAAI,CAAA,EAAAZ,CAAA,QAAAiB,CAAA,QAAAQ,CAAA,gBAAAT,UAAA,cAAAiB,kBAAA,cAAAC,2BAAA,KAAA3B,CAAA,GAAAW,MAAA,CAAAiB,cAAA,MAAArB,CAAA,MAAAJ,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAU,mBAAA,CAAAb,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAU,CAAA,GAAAiB,0BAAA,CAAAnB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAA1I,CAAA,WAAAuI,MAAA,CAAAkB,cAAA,GAAAlB,MAAA,CAAAkB,cAAA,CAAAzJ,CAAA,EAAAuJ,0BAAA,KAAAvJ,CAAA,CAAA0J,SAAA,GAAAH,0BAAA,EAAAd,mBAAA,CAAAzI,CAAA,EAAAiI,CAAA,yBAAAjI,CAAA,CAAAoI,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAAtI,CAAA,WAAAsJ,iBAAA,CAAAlB,SAAA,GAAAmB,0BAAA,EAAAd,mBAAA,CAAAH,CAAA,iBAAAiB,0BAAA,GAAAd,mBAAA,CAAAc,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAAlB,mBAAA,CAAAc,0BAAA,EAAAtB,CAAA,wBAAAQ,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAL,CAAA,gBAAAQ,mBAAA,CAAAH,CAAA,EAAAP,CAAA,iCAAAU,mBAAA,CAAAH,CAAA,8DAAAsB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAAxC,CAAA,EAAAyC,CAAA,EAAApB,CAAA;AAAA,SAAAD,oBAAAzI,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAP,CAAA,GAAAkB,MAAA,CAAAwB,cAAA,QAAA1C,CAAA,uBAAArH,CAAA,IAAAqH,CAAA,QAAAoB,mBAAA,YAAAuB,mBAAAhK,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAAR,CAAA,GAAAA,CAAA,CAAArH,CAAA,EAAA6H,CAAA,IAAA9K,KAAA,EAAAgL,CAAA,EAAAkC,UAAA,GAAArC,CAAA,EAAAsC,YAAA,GAAAtC,CAAA,EAAAuC,QAAA,GAAAvC,CAAA,MAAA5H,CAAA,CAAA6H,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAU,mBAAA,CAAAzI,CAAA,EAAA6H,CAAA,YAAA7H,CAAA,gBAAAoK,OAAA,CAAAvC,CAAA,EAAAE,CAAA,EAAA/H,CAAA,UAAAiI,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAQ,mBAAA,CAAAzI,CAAA,EAAA6H,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAAyC,mBAAAtC,CAAA,EAAAH,CAAA,EAAA5H,CAAA,EAAA6H,CAAA,EAAAI,CAAA,EAAAa,CAAA,EAAAX,CAAA,cAAAd,CAAA,GAAAU,CAAA,CAAAe,CAAA,EAAAX,CAAA,GAAAG,CAAA,GAAAjB,CAAA,CAAAtK,KAAA,WAAAgL,CAAA,gBAAA/H,CAAA,CAAA+H,CAAA,KAAAV,CAAA,CAAA+B,IAAA,GAAAxB,CAAA,CAAAU,CAAA,IAAAgC,OAAA,CAAAC,OAAA,CAAAjC,CAAA,EAAAkC,IAAA,CAAA3C,CAAA,EAAAI,CAAA;AAAA,SAAAwC,kBAAA1C,CAAA,6BAAAH,CAAA,SAAA5H,CAAA,GAAAL,SAAA,aAAA2K,OAAA,WAAAzC,CAAA,EAAAI,CAAA,QAAAa,CAAA,GAAAf,CAAA,CAAA2C,KAAA,CAAA9C,CAAA,EAAA5H,CAAA,YAAA2K,MAAA5C,CAAA,IAAAsC,kBAAA,CAAAvB,CAAA,EAAAjB,CAAA,EAAAI,CAAA,EAAA0C,KAAA,EAAAC,MAAA,UAAA7C,CAAA,cAAA6C,OAAA7C,CAAA,IAAAsC,kBAAA,CAAAvB,CAAA,EAAAjB,CAAA,EAAAI,CAAA,EAAA0C,KAAA,EAAAC,MAAA,WAAA7C,CAAA,KAAA4C,KAAA;AAAA,SAAAlO,gBAAAqM,CAAA,EAAAf,CAAA,UAAAe,CAAA,YAAAf,CAAA,aAAAmB,SAAA;AAAA,SAAAmC,kBAAArL,CAAA,EAAA6H,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAAP,MAAA,EAAAM,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAAgC,UAAA,GAAAhC,CAAA,CAAAgC,UAAA,QAAAhC,CAAA,CAAAiC,YAAA,kBAAAjC,CAAA,KAAAA,CAAA,CAAAkC,QAAA,QAAA5B,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,EAAAsL,cAAA,CAAArD,CAAA,CAAAnL,GAAA,GAAAmL,CAAA;AAAA,SAAApL,aAAAmD,CAAA,EAAA6H,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAAwD,iBAAA,CAAArL,CAAA,CAAAoI,SAAA,EAAAP,CAAA,GAAAD,CAAA,IAAAyD,iBAAA,CAAArL,CAAA,EAAA4H,CAAA,GAAAW,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,iBAAAmK,QAAA,SAAAnK,CAAA;AAAA,SAAAtD,gBAAAsD,CAAA,EAAA6H,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAAyD,cAAA,CAAAzD,CAAA,MAAA7H,CAAA,GAAAuI,MAAA,CAAAwB,cAAA,CAAA/J,CAAA,EAAA6H,CAAA,IAAA9K,KAAA,EAAA6K,CAAA,EAAAqC,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAAnK,CAAA,CAAA6H,CAAA,IAAAD,CAAA,EAAA5H,CAAA;AAAA,SAAAsL,eAAA1D,CAAA,QAAAP,CAAA,GAAAkE,YAAA,CAAA3D,CAAA,gCAAAnB,OAAA,CAAAY,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAkE,aAAA3D,CAAA,EAAAC,CAAA,oBAAApB,OAAA,CAAAmB,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAA5H,CAAA,GAAA4H,CAAA,CAAAE,MAAA,CAAA0D,WAAA,kBAAAxL,CAAA,QAAAqH,CAAA,GAAArH,CAAA,CAAAmJ,IAAA,CAAAvB,CAAA,EAAAC,CAAA,gCAAApB,OAAA,CAAAY,CAAA,UAAAA,CAAA,YAAA6B,SAAA,yEAAArB,CAAA,GAAA4D,MAAA,GAAAC,MAAA,EAAA9D,CAAA;AADA;AACA;AACA;;AAEgD;AACJ;AACI;AAY/B;;AAEjB;AACA;AACA;AACO,IAAM6O,QAAQ;EAAA,SAAAA,SAAA;IAAAha,eAAA,OAAAga,QAAA;EAAA;EAAA,OAAA5Z,YAAA,CAAA4Z,QAAA;IAAA3Z,GAAA;IAAAC,KAAA;IAInB;AACF;AACA;IACE,SAAcuP,SAASA,CAACE,MAA8B,EAAQ;MAC5D,IAAI,CAACkK,OAAO,CAACpK,SAAS,CAACE,MAAM,CAAC;IAChC;;IAEA;AACF;AACA;EAFE;IAAA1P,GAAA;IAAAC,KAAA;MAAA,IAAA0P,UAAA,GAAAhC,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAA4C,QAA8BF,MAAiB;QAAA,IAAAkC,WAAA,EAAA0C,KAAA,EAAAgD,EAAA;QAAA,OAAAxK,YAAA,GAAAC,CAAA,WAAA8C,QAAA;UAAA,kBAAAA,QAAA,CAAA5E,CAAA;YAAA;cAAA4E,QAAA,CAAAhE,CAAA;cAAAgE,QAAA,CAAA5E,CAAA;cAAA,OAEjB,IAAI,CAAC2O,OAAO,CAACvJ,SAAS,CAACX,MAAM,CAAC;YAAA;cAAlDkC,WAAW,GAAA/B,QAAA,CAAA9D,CAAA;cACXuI,KAAK,GAAG,IAAID,qDAAQ,CAACzC,WAAW,CAAC;cAAA,OAAA/B,QAAA,CAAA7D,CAAA,IAChCsI,KAAK,CAACyB,QAAQ,CAAC,CAAC;YAAA;cAAAlG,QAAA,CAAAhE,CAAA;cAAAyL,EAAA,GAAAzH,QAAA,CAAA9D,CAAA;cAAA,MAEnBuL,EAAA,YAAiB/X,+CAAW;gBAAAsQ,QAAA,CAAA5E,CAAA;gBAAA;cAAA;cAAA,MAAAqM,EAAA;YAAA;cAAA,MAG1B,IAAI/X,+CAAW,0BAAAuC,MAAA,CAA0BwV,EAAA,CAAM9U,OAAO,CAAE,CAAC;YAAA;cAAA,OAAAqN,QAAA,CAAA7D,CAAA;UAAA;QAAA,GAAA4D,OAAA;MAAA,CAElE;MAAA,SAXmBS,SAASA,CAAAC,EAAA;QAAA,OAAAX,UAAA,CAAA/B,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAATwN,SAAS;IAAA;IAa7B;AACF;AACA;IAFE;EAAA;IAAArQ,GAAA;IAAAC,KAAA,EAGA,SAAcmU,eAAeA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAACwF,OAAO,CAACxF,eAAe,CAAC,CAAC;IACvC;;IAEA;AACF;AACA;EAFE;IAAApU,GAAA;IAAAC,KAAA;MAAA,IAAA4Z,WAAA,GAAAlM,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAGA,SAAAwD,SAAA;QAAA,IAAApQ,IAAA;UAAA2X,SAAA;UAAA+B,MAAA,GAAAjX,SAAA;QAAA,OAAAiK,YAAA,GAAAC,CAAA,WAAA6D,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,CAAA;YAAA;cAA+B7K,IAAyB,GAAA0Z,MAAA,CAAAtP,MAAA,QAAAsP,MAAA,QAAA5E,SAAA,GAAA4E,MAAA,MAAG,KAAK;cACxD/B,SAA0B,GAAG,EAAE;cAErC,IAAI3X,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,IAAI,EAAE;gBACnC2X,SAAS,CAAC3J,IAAI,CACZ,IAAI,CAACkB,MAAM,CAACyC,YAAY,CAAC,CAAC,EAC1B,IAAI,CAACzC,MAAM,CAAC0C,mBAAmB,CAAC,CAClC,CAAC;cACH;cAEA,IAAI5R,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,IAAI,EAAE;gBACnC2X,SAAS,CAAC3J,IAAI,CACZ,IAAI,CAACkB,MAAM,CAACqD,cAAc,CAAC,CAAC,EAC5B,IAAI,CAACrD,MAAM,CAACsD,aAAa,CAAC,CAC5B,CAAC;cACH;cAAChC,SAAA,CAAA3F,CAAA;cAAA,OAEKuC,OAAO,CAACsE,GAAG,CAACiG,SAAS,CAAC;YAAA;cAAA,OAAAnH,SAAA,CAAA5E,CAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA,CAC7B;MAAA,SAlBmBuJ,UAAUA,CAAA;QAAA,OAAAF,WAAA,CAAAjM,KAAA,OAAA/K,SAAA;MAAA;MAAA,OAAVkX,UAAU;IAAA;IAoB9B;AACF;AACA;IAFE;EAAA;IAAA/Z,GAAA;IAAAC,KAAA,EAGA,SAAc+Z,WAAWA,CAAC5Z,IAAiB,EAAW;MACpD,IAAM6Z,KAAK,GAAG,IAAI,CAAC7F,eAAe,CAAC,CAAC;MAEpC,IAAIhU,IAAI,KAAK,IAAI,EAAE;QACjB,OAAO,CAAC,EAAE6Z,KAAK,CAACjL,QAAQ,IAAIiL,KAAK,CAAC7K,eAAe,CAAC;MACpD,CAAC,MAAM;QACL,OAAO,CAAC,EAAE6K,KAAK,CAAC9K,UAAU,IAAI8K,KAAK,CAAC5K,SAAS,CAAC;MAChD;IACF;;IAEA;AACF;AACA;EAFE;IAAArP,GAAA;IAAAC,KAAA,EAGA,SAAckV,UAAUA,CAAA,EAAW;MACjC,OAAO,OAAO;IAChB;EAAC;AAAA;;AAGH;AACA;AACA;AAFAvV,eAAA,CA9Ea+Z,QAAQ,aACkB5K,yDAAU,CAACQ,WAAW,CAAC,CAAC;AAAA3P,eAAA,CADlD+Z,QAAQ,YAEqB7K,yDAAc,CAACS,WAAW,CAAC,CAAC;AA+E/D,SAAec,SAASA,CAAAU,GAAA;EAAA,OAAAmJ,WAAA,CAAAtM,KAAA,OAAA/K,SAAA;AAAA;;AAI/B;AACA;AACA;AAFA,SAAAqX,YAAA;EAAAA,WAAA,GAAAvM,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAJO,SAAA0E,SAAyBhC,MAAiB;IAAA,OAAA5C,YAAA,GAAAC,CAAA,WAAA8E,SAAA;MAAA,kBAAAA,SAAA,CAAA5G,CAAA;QAAA;UAAA,OAAA4G,SAAA,CAAA7F,CAAA,IACxC2N,QAAQ,CAACtJ,SAAS,CAACX,MAAM,CAAC;MAAA;IAAA,GAAAgC,QAAA;EAAA,CAClC;EAAA,OAAAwI,WAAA,CAAAtM,KAAA,OAAA/K,SAAA;AAAA;AAKM,SAAeoN,WAAWA,CAAAe,GAAA;EAAA,OAAAS,YAAA,CAAA7D,KAAA,OAAA/K,SAAA;AAAA;;AAIjC;AACA;AACA;AAFA,SAAA4O,aAAA;EAAAA,YAAA,GAAA9D,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAJO,SAAAwF,SAA2B9C,MAAiC;IAAA,OAAA5C,YAAA,GAAAC,CAAA,WAAA2F,SAAA;MAAA,kBAAAA,SAAA,CAAAzH,CAAA;QAAA;UAAA,OAAAyH,SAAA,CAAA1G,CAAA,IAC1D2N,QAAQ,CAACtJ,SAAS,CAAA3O,aAAA,CAAAA,aAAA,KAAMgO,MAAM;YAAEtP,IAAI,EAAEyO,2CAAO,CAACmB;UAAM,EAAE,CAAC;MAAA;IAAA,GAAAwC,QAAA;EAAA,CAC/D;EAAA,OAAAf,YAAA,CAAA7D,KAAA,OAAA/K,SAAA;AAAA;AAKM,SAAesN,WAAWA,CAAAkC,GAAA;EAAA,OAAAE,YAAA,CAAA3E,KAAA,OAAA/K,SAAA;AAAA;;AAIjC;AACA;AACA;AAFA,SAAA0P,aAAA;EAAAA,YAAA,GAAA5E,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAJO,SAAAiL,SAA2BvI,MAAiC;IAAA,OAAA5C,YAAA,GAAAC,CAAA,WAAAoL,SAAA;MAAA,kBAAAA,SAAA,CAAAlN,CAAA;QAAA;UAAA,OAAAkN,SAAA,CAAAnM,CAAA,IAC1D2N,QAAQ,CAACtJ,SAAS,CAAA3O,aAAA,CAAAA,aAAA,KAAMgO,MAAM;YAAEtP,IAAI,EAAEyO,2CAAO,CAACqB;UAAM,EAAE,CAAC;MAAA;IAAA,GAAA+H,QAAA;EAAA,CAC/D;EAAA,OAAA1F,YAAA,CAAA3E,KAAA,OAAA/K,SAAA;AAAA;AAKM,SAAesX,aAAaA,CAAA7H,GAAA;EAAA,OAAA8H,cAAA,CAAAxM,KAAA,OAAA/K,SAAA;AAAA;;AAInC;AAAA,SAAAuX,eAAA;EAAAA,cAAA,GAAAzM,iBAAA,cAAAb,YAAA,GAAAE,CAAA,CAJO,SAAAsL,SAA6B5I,MAA+B;IAAA,OAAA5C,YAAA,GAAAC,CAAA,WAAAwL,SAAA;MAAA,kBAAAA,SAAA,CAAAtN,CAAA;QAAA;UAAA,OAAAsN,SAAA,CAAAvM,CAAA,IAC1D2N,QAAQ,CAACtJ,SAAS,CAACX,MAAmB,CAAC;MAAA;IAAA,GAAA4I,QAAA;EAAA,CAC/C;EAAA,OAAA8B,cAAA,CAAAxM,KAAA,OAAA/K,SAAA;AAAA;AAaY;;AAGb;AACgD;;AAEhD;AACA,iEAAe8W,QAAQ,EAAC;;AAExB;AACA,IAAI,OAAOxS,MAAM,KAAK,WAAW,EAAE;EAChCA,MAAM,CAASwS,QAAQ,GAAGA,QAAQ;EAClCxS,MAAM,CAASkJ,SAAS,GAAGA,SAAS;EACpClJ,MAAM,CAAS8I,WAAW,GAAGA,WAAW;EACxC9I,MAAM,CAASgJ,WAAW,GAAGA,WAAW;EACxChJ,MAAM,CAASgT,aAAa,GAAGA,aAAa;AAC/C,C", "sources": ["webpack://SGMapSDK/webpack/universalModuleDefinition", "webpack://SGMapSDK/./src/adapters/base-adapter.ts", "webpack://SGMapSDK/./src/adapters/mapbox-adapter.ts", "webpack://SGMapSDK/./src/adapters/supermap3d-adapter.ts", "webpack://SGMapSDK/./src/core/map-factory.ts", "webpack://SGMapSDK/./src/core/map-proxy.ts", "webpack://SGMapSDK/./src/types/index.ts", "webpack://SGMapSDK/./src/utils/loader.ts", "webpack://SGMapSDK/webpack/bootstrap", "webpack://SGMapSDK/webpack/runtime/define property getters", "webpack://SGMapSDK/webpack/runtime/hasOwnProperty shorthand", "webpack://SGMapSDK/./src/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"SGMapSDK\"] = factory();\n\telse\n\t\troot[\"SGMapSDK\"] = factory();\n})(this, () => {\nreturn ", "/**\n * 基础适配器 - 定义统一的地图接口\n */\n\nimport { IMapInstance, LayerConfig, SourceConfig, MapEvent, MapSDKError } from '../types';\n\nexport abstract class BaseMapAdapter implements IMapInstance {\n  protected instance: any;\n  protected container: HTMLElement;\n  protected eventListeners: Map<string, Set<Function>> = new Map();\n\n  constructor(instance: any, container: HTMLElement) {\n    this.instance = instance;\n    this.container = container;\n    this.setupEventForwarding();\n  }\n\n  /**\n   * 设置事件转发机制\n   */\n  protected abstract setupEventForwarding(): void;\n\n  /**\n   * 标准化事件对象\n   */\n  protected normalizeEvent(originalEvent: any, type: string): MapEvent {\n    return {\n      type,\n      target: this,\n      originalEvent: originalEvent.originalEvent || originalEvent,\n      point: originalEvent.point,\n      lngLat: originalEvent.lngLat,\n      features: originalEvent.features\n    };\n  }\n\n  // 基础方法\n  public getContainer(): HTMLElement {\n    return this.container;\n  }\n\n  public abstract getCenter(): [number, number];\n  public abstract setCenter(center: [number, number]): this;\n  public abstract getZoom(): number;\n  public abstract setZoom(zoom: number): this;\n  public abstract getBearing(): number;\n  public abstract setBearing(bearing: number): this;\n  public abstract getPitch(): number;\n  public abstract setPitch(pitch: number): this;\n\n  // 图层管理\n  public abstract addLayer(layer: LayerConfig): this;\n  public abstract removeLayer(layerId: string): this;\n  public abstract getLayer(layerId: string): any;\n  public abstract moveLayer(layerId: string, beforeId?: string): this;\n\n  // 数据源管理\n  public abstract addSource(sourceId: string, source: SourceConfig): this;\n  public abstract removeSource(sourceId: string): this;\n  public abstract getSource(sourceId: string): any;\n\n  // 事件管理\n  public on(type: string, listener: (event: MapEvent) => void): this {\n    if (!this.eventListeners.has(type)) {\n      this.eventListeners.set(type, new Set());\n    }\n    this.eventListeners.get(type)!.add(listener);\n    return this;\n  }\n\n  public off(type: string, listener?: (event: MapEvent) => void): this {\n    if (!this.eventListeners.has(type)) {\n      return this;\n    }\n\n    const listeners = this.eventListeners.get(type)!;\n    if (listener) {\n      listeners.delete(listener);\n    } else {\n      listeners.clear();\n    }\n\n    if (listeners.size === 0) {\n      this.eventListeners.delete(type);\n    }\n\n    return this;\n  }\n\n  public fire(type: string, data?: any): this {\n    if (!this.eventListeners.has(type)) {\n      return this;\n    }\n\n    const event: MapEvent = {\n      type,\n      target: this,\n      ...data\n    };\n\n    this.eventListeners.get(type)!.forEach(listener => {\n      try {\n        listener(event);\n      } catch (error) {\n        console.error(`Error in event listener for ${type}:`, error);\n      }\n    });\n\n    return this;\n  }\n\n  // 控制方法\n  public abstract resize(): this;\n  public abstract remove(): void;\n\n  // 获取原始实例\n  public getOriginalInstance(): any {\n    return this.instance;\n  }\n\n  /**\n   * 验证图层配置\n   */\n  protected validateLayerConfig(layer: LayerConfig): void {\n    if (!layer.id) {\n      throw new MapSDKError('Layer must have an id');\n    }\n    if (!layer.type) {\n      throw new MapSDKError('Layer must have a type');\n    }\n  }\n\n  /**\n   * 验证数据源配置\n   */\n  protected validateSourceConfig(source: SourceConfig): void {\n    if (!source.type) {\n      throw new MapSDKError('Source must have a type');\n    }\n  }\n\n  /**\n   * 安全执行方法\n   */\n  protected safeExecute<T>(fn: () => T, errorMessage: string): T {\n    try {\n      return fn();\n    } catch (error) {\n      throw new MapSDKError(`${errorMessage}: ${error.message}`);\n    }\n  }\n\n  /**\n   * 检查实例是否有效\n   */\n  protected checkInstance(): void {\n    if (!this.instance) {\n      throw new MapSDKError('Map instance is not available');\n    }\n  }\n}\n", "/**\n * MapboxGL适配器 - 适配MapboxGL地图\n */\n\nimport { BaseMapAdapter } from './base-adapter';\nimport { LayerConfig, SourceConfig, MapSDKError } from '../types';\n\nexport class MapboxAdapter extends BaseMapAdapter {\n  \n  /**\n   * 设置事件转发机制\n   */\n  protected setupEventForwarding(): void {\n    if (!this.instance) return;\n\n    // 常用的地图事件\n    const events = [\n      'load', 'idle', 'remove', 'render', 'resize',\n      'webglcontextlost', 'webglcontextrestored', 'dataloading',\n      'data', 'tiledataloading', 'sourcedataloading', 'styledataloading',\n      'sourcedata', 'styledata', 'boxzoomcancel', 'boxzoomstart',\n      'boxzoomend', 'touchcancel', 'touchmove', 'touchend', 'touchstart',\n      'click', 'contextmenu', 'dblclick', 'mousemove', 'mouseup',\n      'mousedown', 'mouseout', 'mouseover', 'movestart', 'move',\n      'moveend', 'zoomstart', 'zoom', 'zoomend', 'rotatestart',\n      'rotate', 'rotateend', 'dragstart', 'drag', 'dragend',\n      'pitchstart', 'pitch', 'pitchend', 'wheel'\n    ];\n\n    events.forEach(eventType => {\n      this.instance.on(eventType, (e: any) => {\n        const normalizedEvent = this.normalizeEvent(e, eventType);\n        this.fire(eventType, normalizedEvent);\n      });\n    });\n  }\n\n  // 基础方法实现\n  public getCenter(): [number, number] {\n    this.checkInstance();\n    const center = this.instance.getCenter();\n    return [center.lng, center.lat];\n  }\n\n  public setCenter(center: [number, number]): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.setCenter(center);\n    }, 'Failed to set center');\n    return this;\n  }\n\n  public getZoom(): number {\n    this.checkInstance();\n    return this.instance.getZoom();\n  }\n\n  public setZoom(zoom: number): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.setZoom(zoom);\n    }, 'Failed to set zoom');\n    return this;\n  }\n\n  public getBearing(): number {\n    this.checkInstance();\n    return this.instance.getBearing();\n  }\n\n  public setBearing(bearing: number): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.setBearing(bearing);\n    }, 'Failed to set bearing');\n    return this;\n  }\n\n  public getPitch(): number {\n    this.checkInstance();\n    return this.instance.getPitch();\n  }\n\n  public setPitch(pitch: number): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.setPitch(pitch);\n    }, 'Failed to set pitch');\n    return this;\n  }\n\n  // 图层管理\n  public addLayer(layer: LayerConfig): this {\n    this.checkInstance();\n    this.validateLayerConfig(layer);\n    \n    this.safeExecute(() => {\n      this.instance.addLayer(layer);\n    }, `Failed to add layer ${layer.id}`);\n    \n    return this;\n  }\n\n  public removeLayer(layerId: string): this {\n    this.checkInstance();\n    \n    this.safeExecute(() => {\n      if (this.instance.getLayer(layerId)) {\n        this.instance.removeLayer(layerId);\n      }\n    }, `Failed to remove layer ${layerId}`);\n    \n    return this;\n  }\n\n  public getLayer(layerId: string): any {\n    this.checkInstance();\n    return this.instance.getLayer(layerId);\n  }\n\n  public moveLayer(layerId: string, beforeId?: string): this {\n    this.checkInstance();\n    \n    this.safeExecute(() => {\n      this.instance.moveLayer(layerId, beforeId);\n    }, `Failed to move layer ${layerId}`);\n    \n    return this;\n  }\n\n  // 数据源管理\n  public addSource(sourceId: string, source: SourceConfig): this {\n    this.checkInstance();\n    this.validateSourceConfig(source);\n    \n    this.safeExecute(() => {\n      this.instance.addSource(sourceId, source);\n    }, `Failed to add source ${sourceId}`);\n    \n    return this;\n  }\n\n  public removeSource(sourceId: string): this {\n    this.checkInstance();\n    \n    this.safeExecute(() => {\n      if (this.instance.getSource(sourceId)) {\n        this.instance.removeSource(sourceId);\n      }\n    }, `Failed to remove source ${sourceId}`);\n    \n    return this;\n  }\n\n  public getSource(sourceId: string): any {\n    this.checkInstance();\n    return this.instance.getSource(sourceId);\n  }\n\n  // 控制方法\n  public resize(): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.resize();\n    }, 'Failed to resize map');\n    return this;\n  }\n\n  public remove(): void {\n    if (this.instance) {\n      this.safeExecute(() => {\n        this.instance.remove();\n      }, 'Failed to remove map');\n      this.instance = null;\n    }\n    this.eventListeners.clear();\n  }\n\n  /**\n   * MapboxGL特有方法\n   */\n  \n  /**\n   * 设置样式\n   */\n  public setStyle(style: string | object): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.setStyle(style);\n    }, 'Failed to set style');\n    return this;\n  }\n\n  /**\n   * 获取样式\n   */\n  public getStyle(): any {\n    this.checkInstance();\n    return this.instance.getStyle();\n  }\n\n  /**\n   * 飞行到指定位置\n   */\n  public flyTo(options: any): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.flyTo(options);\n    }, 'Failed to fly to location');\n    return this;\n  }\n\n  /**\n   * 跳转到指定位置\n   */\n  public jumpTo(options: any): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.jumpTo(options);\n    }, 'Failed to jump to location');\n    return this;\n  }\n\n  /**\n   * 适配边界\n   */\n  public fitBounds(bounds: [[number, number], [number, number]], options?: any): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.fitBounds(bounds, options);\n    }, 'Failed to fit bounds');\n    return this;\n  }\n\n  /**\n   * 查询渲染的要素\n   */\n  public queryRenderedFeatures(pointOrBox?: any, options?: any): any[] {\n    this.checkInstance();\n    return this.safeExecute(() => {\n      return this.instance.queryRenderedFeatures(pointOrBox, options);\n    }, 'Failed to query rendered features');\n  }\n\n  /**\n   * 查询源数据要素\n   */\n  public querySourceFeatures(sourceId: string, options?: any): any[] {\n    this.checkInstance();\n    return this.safeExecute(() => {\n      return this.instance.querySourceFeatures(sourceId, options);\n    }, 'Failed to query source features');\n  }\n\n  /**\n   * 屏幕坐标转地理坐标\n   */\n  public unproject(point: [number, number]): [number, number] {\n    this.checkInstance();\n    const lngLat = this.instance.unproject(point);\n    return [lngLat.lng, lngLat.lat];\n  }\n\n  /**\n   * 地理坐标转屏幕坐标\n   */\n  public project(lngLat: [number, number]): [number, number] {\n    this.checkInstance();\n    const point = this.instance.project(lngLat);\n    return [point.x, point.y];\n  }\n}\n", "/**\n * SuperMap3D适配器 - 适配SuperMap3D场景\n */\n\nimport { BaseMapAdapter } from './base-adapter';\nimport { LayerConfig, SourceConfig, MapSDKError } from '../types';\n\nexport class SuperMap3DAdapter extends BaseMapAdapter {\n  \n  /**\n   * 设置事件转发机制\n   */\n  protected setupEventForwarding(): void {\n    if (!this.instance) return;\n\n    // SuperMap3D的事件处理\n    const scene = this.instance.scene;\n    const camera = this.instance.camera;\n    \n    if (scene && scene.canvas) {\n      // 创建事件处理器\n      const handler = new (window as any).SuperMap3D.ScreenSpaceEventHandler(scene.canvas);\n      \n      // 鼠标点击事件\n      handler.setInputAction((event: any) => {\n        const pickedPosition = scene.pickPosition(event.position);\n        const cartographic = (window as any).SuperMap3D.Cartographic.fromCartesian(pickedPosition);\n        \n        if (cartographic) {\n          const longitude = (window as any).SuperMap3D.Math.toDegrees(cartographic.longitude);\n          const latitude = (window as any).SuperMap3D.Math.toDegrees(cartographic.latitude);\n          \n          const normalizedEvent = this.normalizeEvent({\n            originalEvent: event,\n            point: [event.position.x, event.position.y],\n            lngLat: [longitude, latitude]\n          }, 'click');\n          \n          this.fire('click', normalizedEvent);\n        }\n      }, (window as any).SuperMap3D.ScreenSpaceEventType.LEFT_CLICK);\n\n      // 鼠标移动事件\n      handler.setInputAction((event: any) => {\n        const normalizedEvent = this.normalizeEvent({\n          originalEvent: event,\n          point: [event.endPosition.x, event.endPosition.y]\n        }, 'mousemove');\n        \n        this.fire('mousemove', normalizedEvent);\n      }, (window as any).SuperMap3D.ScreenSpaceEventType.MOUSE_MOVE);\n\n      // 相机移动事件\n      if (camera) {\n        camera.moveStart.addEventListener(() => {\n          this.fire('movestart', { type: 'movestart', target: this });\n        });\n\n        camera.moveEnd.addEventListener(() => {\n          this.fire('moveend', { type: 'moveend', target: this });\n        });\n      }\n\n      // 场景渲染事件\n      if (scene.postRender) {\n        scene.postRender.addEventListener(() => {\n          this.fire('render', { type: 'render', target: this });\n        });\n      }\n    }\n  }\n\n  // 基础方法实现\n  public getCenter(): [number, number] {\n    this.checkInstance();\n    const camera = this.instance.camera;\n    const cartographic = (window as any).SuperMap3D.Cartographic.fromCartesian(camera.position);\n    const longitude = (window as any).SuperMap3D.Math.toDegrees(cartographic.longitude);\n    const latitude = (window as any).SuperMap3D.Math.toDegrees(cartographic.latitude);\n    return [longitude, latitude];\n  }\n\n  public setCenter(center: [number, number]): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      const destination = (window as any).SuperMap3D.Cartesian3.fromDegrees(center[0], center[1], 1000);\n      this.instance.camera.setView({ destination });\n    }, 'Failed to set center');\n    return this;\n  }\n\n  public getZoom(): number {\n    this.checkInstance();\n    // SuperMap3D没有直接的zoom概念，这里通过相机高度模拟\n    const camera = this.instance.camera;\n    const cartographic = (window as any).SuperMap3D.Cartographic.fromCartesian(camera.position);\n    const height = cartographic.height;\n    // 将高度转换为类似zoom级别的值\n    return Math.log2(40075016.686 / height) - 8;\n  }\n\n  public setZoom(zoom: number): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      // 将zoom级别转换为相机高度\n      const height = 40075016.686 / Math.pow(2, zoom + 8);\n      const camera = this.instance.camera;\n      const cartographic = (window as any).SuperMap3D.Cartographic.fromCartesian(camera.position);\n      const longitude = (window as any).SuperMap3D.Math.toDegrees(cartographic.longitude);\n      const latitude = (window as any).SuperMap3D.Math.toDegrees(cartographic.latitude);\n      \n      const destination = (window as any).SuperMap3D.Cartesian3.fromDegrees(longitude, latitude, height);\n      camera.setView({ destination });\n    }, 'Failed to set zoom');\n    return this;\n  }\n\n  public getBearing(): number {\n    this.checkInstance();\n    const camera = this.instance.camera;\n    return (window as any).SuperMap3D.Math.toDegrees(camera.heading);\n  }\n\n  public setBearing(bearing: number): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      const camera = this.instance.camera;\n      const heading = (window as any).SuperMap3D.Math.toRadians(bearing);\n      camera.setView({ \n        orientation: {\n          heading,\n          pitch: camera.pitch,\n          roll: camera.roll\n        }\n      });\n    }, 'Failed to set bearing');\n    return this;\n  }\n\n  public getPitch(): number {\n    this.checkInstance();\n    const camera = this.instance.camera;\n    return (window as any).SuperMap3D.Math.toDegrees(camera.pitch);\n  }\n\n  public setPitch(pitch: number): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      const camera = this.instance.camera;\n      const pitchRadians = (window as any).SuperMap3D.Math.toRadians(pitch);\n      camera.setView({ \n        orientation: {\n          heading: camera.heading,\n          pitch: pitchRadians,\n          roll: camera.roll\n        }\n      });\n    }, 'Failed to set pitch');\n    return this;\n  }\n\n  // 图层管理（SuperMap3D的图层概念不同，这里做适配）\n  public addLayer(layer: LayerConfig): this {\n    this.checkInstance();\n    this.validateLayerConfig(layer);\n    \n    this.safeExecute(() => {\n      const scene = this.instance.scene;\n      \n      // 根据图层类型进行不同处理\n      switch (layer.type) {\n        case 'imagery':\n          this.addImageryLayer(layer);\n          break;\n        case 's3m':\n          this.addS3MLayer(layer);\n          break;\n        case 'terrain':\n          this.addTerrainLayer(layer);\n          break;\n        case 'mvt':\n          this.addMVTLayer(layer);\n          break;\n        default:\n          console.warn(`Layer type ${layer.type} is not supported in 3D mode`);\n      }\n    }, `Failed to add layer ${layer.id}`);\n    \n    return this;\n  }\n\n  private addImageryLayer(layer: LayerConfig): void {\n    const scene = this.instance.scene;\n    const imageryLayers = this.instance.imageryLayers;\n    \n    if (layer.source && typeof layer.source === 'object' && (layer.source as any).url) {\n      const provider = new (window as any).SuperMap3D.SuperMapImageryProvider({\n        url: (layer.source as any).url\n      });\n      imageryLayers.addImageryProvider(provider);\n    }\n  }\n\n  private addS3MLayer(layer: LayerConfig): void {\n    const scene = this.instance.scene;\n    \n    if (layer.source && typeof layer.source === 'object' && (layer.source as any).url) {\n      scene.addS3MTilesLayerByScp((layer.source as any).url, {\n        name: layer.id\n      });\n    }\n  }\n\n  private addTerrainLayer(layer: LayerConfig): void {\n    if (layer.source && typeof layer.source === 'object' && (layer.source as any).url) {\n      const terrainProvider = new (window as any).SuperMap3D.SuperMapTerrainProvider({\n        url: (layer.source as any).url,\n        isSct: true\n      });\n      this.instance.terrainProvider = terrainProvider;\n    }\n  }\n\n  private addMVTLayer(layer: LayerConfig): void {\n    const scene = this.instance.scene;\n    \n    if (layer.source && typeof layer.source === 'object' && (layer.source as any).url) {\n      scene.addVectorTilesMap({\n        url: (layer.source as any).url,\n        name: layer.id,\n        viewer: this.instance\n      });\n    }\n  }\n\n  public removeLayer(layerId: string): this {\n    this.checkInstance();\n    \n    this.safeExecute(() => {\n      // SuperMap3D的图层移除需要根据类型进行不同处理\n      const scene = this.instance.scene;\n      const imageryLayers = this.instance.imageryLayers;\n      \n      // 尝试从影像图层中移除\n      for (let i = 0; i < imageryLayers.length; i++) {\n        const layer = imageryLayers.get(i);\n        if (layer.name === layerId) {\n          imageryLayers.remove(layer);\n          break;\n        }\n      }\n      \n      // 尝试从S3M图层中移除\n      if (scene.layers) {\n        scene.layers.removeAll();\n      }\n    }, `Failed to remove layer ${layerId}`);\n    \n    return this;\n  }\n\n  public getLayer(layerId: string): any {\n    this.checkInstance();\n    // SuperMap3D的图层获取比较复杂，这里简化处理\n    return null;\n  }\n\n  public moveLayer(layerId: string, beforeId?: string): this {\n    this.checkInstance();\n    console.warn('moveLayer is not fully supported in 3D mode');\n    return this;\n  }\n\n  // 数据源管理\n  public addSource(sourceId: string, source: SourceConfig): this {\n    this.checkInstance();\n    this.validateSourceConfig(source);\n    \n    // SuperMap3D的数据源概念不同，这里做简单适配\n    console.warn('addSource in 3D mode is handled through layer configuration');\n    return this;\n  }\n\n  public removeSource(sourceId: string): this {\n    this.checkInstance();\n    console.warn('removeSource in 3D mode is handled through layer management');\n    return this;\n  }\n\n  public getSource(sourceId: string): any {\n    this.checkInstance();\n    return null;\n  }\n\n  // 控制方法\n  public resize(): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      this.instance.resize();\n    }, 'Failed to resize scene');\n    return this;\n  }\n\n  public remove(): void {\n    if (this.instance) {\n      this.safeExecute(() => {\n        this.instance.destroy();\n      }, 'Failed to destroy scene');\n      this.instance = null;\n    }\n    this.eventListeners.clear();\n  }\n\n  /**\n   * SuperMap3D特有方法\n   */\n  \n  /**\n   * 飞行到指定位置\n   */\n  public flyTo(options: any): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      const camera = this.instance.camera;\n      camera.flyTo(options);\n    }, 'Failed to fly to location');\n    return this;\n  }\n\n  /**\n   * 设置相机视角\n   */\n  public setView(options: any): this {\n    this.checkInstance();\n    this.safeExecute(() => {\n      const camera = this.instance.camera;\n      camera.setView(options);\n    }, 'Failed to set view');\n    return this;\n  }\n\n  /**\n   * 获取场景对象\n   */\n  public getScene(): any {\n    this.checkInstance();\n    return this.instance.scene;\n  }\n\n  /**\n   * 获取相机对象\n   */\n  public getCamera(): any {\n    this.checkInstance();\n    return this.instance.camera;\n  }\n}\n", "/**\n * 地图工厂 - 根据配置创建相应的地图实例\n */\n\nimport { MapConfig, MapType, Map2DConfig, Map3DConfig, IMapInstance, FactoryConfig, MapSDKError } from '../types';\nimport { ResourceLoader } from '../utils/loader';\nimport { MapboxAdapter } from '../adapters/mapbox-adapter';\nimport { SuperMap3DAdapter } from '../adapters/supermap3d-adapter';\n\nexport class MapFactory {\n  private static instance: MapFactory;\n  private loader: ResourceLoader;\n  private defaultConfig: FactoryConfig = {\n    mapboxgl: {\n      js: 'https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.js',\n      css: 'https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.css'\n    },\n    supermap3d: {\n      js: './iclient-3d/SuperMap3D/SuperMap3D.js',\n      css: './iclient-3d/SuperMap3D/Widgets/widgets.css'\n    },\n    iclientMapboxgl: {\n      js: './iclient-mapboxgl/iclient-mapboxgl.js',\n      css: './iclient-mapboxgl/iclient-mapboxgl.css'\n    },\n    iclient3d: {\n      js: './iclient-3d/deps.js'\n    }\n  };\n\n  private constructor() {\n    this.loader = ResourceLoader.getInstance();\n    this.loader.setConfig(this.defaultConfig);\n  }\n\n  public static getInstance(): MapFactory {\n    if (!MapFactory.instance) {\n      MapFactory.instance = new MapFactory();\n    }\n    return MapFactory.instance;\n  }\n\n  /**\n   * 设置SDK资源配置\n   */\n  public setConfig(config: Partial<FactoryConfig>): void {\n    this.defaultConfig = { ...this.defaultConfig, ...config };\n    this.loader.setConfig(this.defaultConfig);\n  }\n\n  /**\n   * 创建地图实例\n   */\n  public async createMap(config: MapConfig): Promise<IMapInstance> {\n    this.validateConfig(config);\n\n    const container = this.resolveContainer(config.container);\n    \n    if (config.type === MapType.MAP_2D) {\n      return this.create2DMap(config as Map2DConfig, container);\n    } else if (config.type === MapType.MAP_3D) {\n      return this.create3DMap(config as Map3DConfig, container);\n    } else {\n      // 自动判断模式\n      return this.autoDetectMapType(config, container);\n    }\n  }\n\n  /**\n   * 自动判断地图类型\n   */\n  private async autoDetectMapType(config: MapConfig, container: HTMLElement): Promise<IMapInstance> {\n    // 根据配置参数自动判断\n    const has3DFeatures = this.detect3DFeatures(config);\n    \n    if (has3DFeatures) {\n      console.log('Auto-detected 3D features, creating 3D map');\n      const config3D: Map3DConfig = { ...config, type: MapType.MAP_3D };\n      return this.create3DMap(config3D, container);\n    } else {\n      console.log('Auto-detected 2D features, creating 2D map');\n      const config2D: Map2DConfig = { ...config, type: MapType.MAP_2D };\n      return this.create2DMap(config2D, container);\n    }\n  }\n\n  /**\n   * 检测3D特征\n   */\n  private detect3DFeatures(config: any): boolean {\n    // 检查是否有3D相关的配置\n    const has3DConfig = !!(\n      config.scene3DOnly ||\n      config.terrainProvider ||\n      config.shadows ||\n      config.globe ||\n      config.skyBox ||\n      config.sceneMode ||\n      config.pitch > 0\n    );\n\n    // 检查样式是否包含3D图层\n    const has3DStyle = config.style && typeof config.style === 'object' && \n      config.style.layers && \n      config.style.layers.some((layer: any) => \n        layer.type === 'fill-extrusion' || \n        layer.type === 'hillshade' ||\n        layer.type === 'raster-dem'\n      );\n\n    return has3DConfig || has3DStyle;\n  }\n\n  /**\n   * 创建2D地图\n   */\n  private async create2DMap(config: Map2DConfig, container: HTMLElement): Promise<IMapInstance> {\n    // 加载MapboxGL和iClient\n    await Promise.all([\n      this.loader.loadMapboxGL(),\n      this.loader.loadIClientMapboxGL()\n    ]);\n\n    const mapboxgl = (window as any).mapboxgl;\n    if (!mapboxgl) {\n      throw new MapSDKError('MapboxGL failed to load');\n    }\n\n    // 创建MapboxGL实例\n    const mapConfig = this.prepare2DConfig(config);\n    const mapInstance = new mapboxgl.Map(mapConfig);\n\n    // 等待地图加载完成\n    await new Promise<void>((resolve, reject) => {\n      mapInstance.on('load', () => resolve());\n      mapInstance.on('error', (e: any) => reject(new MapSDKError(`Map load error: ${e.error?.message || 'Unknown error'}`)));\n      \n      // 设置超时\n      setTimeout(() => reject(new MapSDKError('Map load timeout')), 30000);\n    });\n\n    return new MapboxAdapter(mapInstance, container);\n  }\n\n  /**\n   * 创建3D地图\n   */\n  private async create3DMap(config: Map3DConfig, container: HTMLElement): Promise<IMapInstance> {\n    // 加载SuperMap3D和iClient3D\n    await Promise.all([\n      this.loader.loadSuperMap3D(),\n      this.loader.loadIClient3D()\n    ]);\n\n    const SuperMap3D = (window as any).SuperMap3D;\n    if (!SuperMap3D) {\n      throw new MapSDKError('SuperMap3D failed to load');\n    }\n\n    // 创建SuperMap3D实例\n    const viewerConfig = this.prepare3DConfig(config, container);\n    const viewer = new SuperMap3D.Viewer(container, viewerConfig);\n\n    // 等待场景准备完成\n    await new Promise<void>((resolve, reject) => {\n      if (viewer.scene) {\n        // 设置初始视角\n        if (config.center) {\n          const destination = SuperMap3D.Cartesian3.fromDegrees(\n            config.center[0], \n            config.center[1], \n            config.zoom ? this.zoomToHeight(config.zoom) : 10000\n          );\n          \n          viewer.camera.setView({\n            destination,\n            orientation: {\n              heading: SuperMap3D.Math.toRadians(config.bearing || 0),\n              pitch: SuperMap3D.Math.toRadians(config.pitch || -90),\n              roll: 0\n            }\n          });\n        }\n        \n        resolve();\n      } else {\n        reject(new MapSDKError('Failed to initialize 3D scene'));\n      }\n    });\n\n    return new SuperMap3DAdapter(viewer, container);\n  }\n\n  /**\n   * 准备2D地图配置\n   */\n  private prepare2DConfig(config: Map2DConfig): any {\n    const { type, container, ...mapboxConfig } = config;\n    \n    return {\n      container: typeof container === 'string' ? container : container.id || 'map',\n      center: config.center || [0, 0],\n      zoom: config.zoom || 0,\n      bearing: config.bearing || 0,\n      pitch: config.pitch || 0,\n      ...mapboxConfig\n    };\n  }\n\n  /**\n   * 准备3D场景配置\n   */\n  private prepare3DConfig(config: Map3DConfig, container: HTMLElement): any {\n    const { type, center, zoom, bearing, pitch, ...viewerConfig } = config;\n    \n    return {\n      animation: config.animation !== false,\n      timeline: config.timeline !== false,\n      baseLayerPicker: config.baseLayerPicker !== false,\n      fullscreenButton: config.fullscreenButton !== false,\n      geocoder: config.geocoder !== false,\n      homeButton: config.homeButton !== false,\n      sceneModePicker: config.sceneModePicker !== false,\n      navigationHelpButton: config.navigationHelpButton !== false,\n      ...viewerConfig\n    };\n  }\n\n  /**\n   * 将zoom级别转换为3D相机高度\n   */\n  private zoomToHeight(zoom: number): number {\n    return 40075016.686 / Math.pow(2, zoom + 8);\n  }\n\n  /**\n   * 解析容器\n   */\n  private resolveContainer(container: string | HTMLElement): HTMLElement {\n    if (typeof container === 'string') {\n      const element = document.getElementById(container) || document.querySelector(container);\n      if (!element) {\n        throw new MapSDKError(`Container element not found: ${container}`);\n      }\n      return element as HTMLElement;\n    }\n    return container;\n  }\n\n  /**\n   * 验证配置\n   */\n  private validateConfig(config: MapConfig): void {\n    if (!config) {\n      throw new MapSDKError('Map configuration is required');\n    }\n\n    if (!config.container) {\n      throw new MapSDKError('Container is required');\n    }\n\n    if (config.type && !Object.values(MapType).includes(config.type)) {\n      throw new MapSDKError(`Invalid map type: ${config.type}`);\n    }\n  }\n\n  /**\n   * 获取加载状态\n   */\n  public getLoadingState() {\n    return this.loader.getLoadingState();\n  }\n}\n", "/**\n * 地图代理 - 通过Proxy机制统一不同SDK的接口访问\n */\n\nimport { IMapInstance, MapSDKError } from '../types';\n\nexport class MapProxy {\n  private mapInstance: IMapInstance;\n  private proxy: any;\n\n  constructor(mapInstance: IMapInstance) {\n    this.mapInstance = mapInstance;\n    this.proxy = this.createProxy();\n  }\n\n  /**\n   * 创建代理对象\n   */\n  private createProxy(): any {\n    return new Proxy(this.mapInstance, {\n      get: (target: IMapInstance, property: string | symbol, receiver: any) => {\n        // 首先检查统一接口中是否存在该属性\n        if (property in target) {\n          const value = Reflect.get(target, property, receiver);\n\n          // 如果是方法，绑定正确的this上下文\n          if (typeof value === 'function') {\n            return value.bind(target);\n          }\n\n          return value;\n        }\n\n        // 检查原始实例中是否存在该属性\n        const originalInstance = target.getOriginalInstance();\n        if (originalInstance && property in originalInstance) {\n          const value = originalInstance[property];\n\n          // 如果是方法，绑定原始实例的this上下文\n          if (typeof value === 'function') {\n            return value.bind(originalInstance);\n          }\n\n          return value;\n        }\n\n        // 特殊处理一些常用的属性和方法\n        return this.handleSpecialProperties(target, property as string);\n      },\n\n      set: (target: IMapInstance, property: string | symbol, value: any, receiver: any) => {\n        // 首先尝试设置统一接口的属性\n        if (property in target) {\n          return Reflect.set(target, property, value, receiver);\n        }\n\n        // 尝试设置原始实例的属性\n        const originalInstance = target.getOriginalInstance();\n        if (originalInstance && property in originalInstance) {\n          originalInstance[property] = value;\n          return true;\n        }\n\n        // 如果都不存在，抛出错误\n        throw new MapSDKError(`Property '${String(property)}' does not exist on map instance`);\n      },\n\n      has: (target: IMapInstance, property: string | symbol) => {\n        // 检查统一接口\n        if (property in target) {\n          return true;\n        }\n\n        // 检查原始实例\n        const originalInstance = target.getOriginalInstance();\n        if (originalInstance && property in originalInstance) {\n          return true;\n        }\n\n        return false;\n      },\n\n      ownKeys: (target: IMapInstance) => {\n        const keys = new Set<string | symbol>();\n\n        // 添加统一接口的键\n        Object.getOwnPropertyNames(target).forEach(key => keys.add(key));\n        Object.getOwnPropertySymbols(target).forEach(key => keys.add(key));\n\n        // 添加原始实例的键\n        const originalInstance = target.getOriginalInstance();\n        if (originalInstance) {\n          Object.getOwnPropertyNames(originalInstance).forEach(key => keys.add(key));\n          Object.getOwnPropertySymbols(originalInstance).forEach(key => keys.add(key));\n        }\n\n        return Array.from(keys);\n      },\n\n      getOwnPropertyDescriptor: (target: IMapInstance, property: string | symbol) => {\n        // 首先检查统一接口\n        let descriptor = Object.getOwnPropertyDescriptor(target, property);\n        if (descriptor) {\n          return descriptor;\n        }\n\n        // 检查原始实例\n        const originalInstance = target.getOriginalInstance();\n        if (originalInstance) {\n          descriptor = Object.getOwnPropertyDescriptor(originalInstance, property);\n          if (descriptor) {\n            return descriptor;\n          }\n        }\n\n        return undefined;\n      }\n    });\n  }\n\n  /**\n   * 处理特殊属性和方法\n   */\n  private handleSpecialProperties(target: IMapInstance, property: string): any {\n    const originalInstance = target.getOriginalInstance();\n\n    switch (property) {\n      // 常用的地图属性别名\n      case 'map':\n      case 'viewer':\n      case 'scene':\n        return originalInstance;\n\n      // 版本信息\n      case 'version':\n        return this.getVersion(originalInstance);\n\n      // 类型信息\n      case 'mapType':\n        return this.getMapType(originalInstance);\n\n      // 是否已加载\n      case 'loaded':\n        return this.isLoaded(originalInstance);\n\n      // 容器元素\n      case 'container':\n        return target.getContainer();\n\n      // 如果属性不存在，提供友好的错误信息\n      default:\n        throw new MapSDKError(\n          `Property or method '${property}' does not exist on map instance. ` +\n          `Available methods: ${this.getAvailableMethods(target).join(', ')}`\n        );\n    }\n  }\n\n  /**\n   * 获取版本信息\n   */\n  private getVersion(originalInstance: any): string {\n    if (originalInstance && typeof originalInstance.version === 'string') {\n      return originalInstance.version;\n    }\n\n    // 尝试从全局对象获取版本\n    if (typeof (window as any).mapboxgl !== 'undefined') {\n      return (window as any).mapboxgl.version || 'unknown';\n    }\n\n    if (typeof (window as any).SuperMap3D !== 'undefined') {\n      return (window as any).SuperMap3D.VERSION || 'unknown';\n    }\n\n    return 'unknown';\n  }\n\n  /**\n   * 获取地图类型\n   */\n  private getMapType(originalInstance: any): string {\n    if (originalInstance) {\n      // 检查是否是MapboxGL实例\n      if (originalInstance.getStyle || originalInstance.addLayer) {\n        return '2d';\n      }\n\n      // 检查是否是SuperMap3D实例\n      if (originalInstance.scene || originalInstance.camera) {\n        return '3d';\n      }\n    }\n\n    return 'unknown';\n  }\n\n  /**\n   * 检查是否已加载\n   */\n  private isLoaded(originalInstance: any): boolean {\n    if (originalInstance) {\n      // MapboxGL的loaded状态\n      if (typeof originalInstance.loaded === 'function') {\n        return originalInstance.loaded();\n      }\n\n      // SuperMap3D通常在创建后就是可用的\n      if (originalInstance.scene) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * 获取可用方法列表\n   */\n  private getAvailableMethods(target: IMapInstance): string[] {\n    const methods: string[] = [];\n\n    // 获取统一接口的方法\n    let obj = target;\n    while (obj && obj !== Object.prototype) {\n      Object.getOwnPropertyNames(obj).forEach(name => {\n        if (typeof (obj as any)[name] === 'function' && !methods.includes(name)) {\n          methods.push(name);\n        }\n      });\n      obj = Object.getPrototypeOf(obj);\n    }\n\n    // 获取原始实例的方法\n    const originalInstance = target.getOriginalInstance();\n    if (originalInstance) {\n      let originalObj = originalInstance;\n      while (originalObj && originalObj !== Object.prototype) {\n        Object.getOwnPropertyNames(originalObj).forEach(name => {\n          if (typeof originalObj[name] === 'function' && !methods.includes(name)) {\n            methods.push(name);\n          }\n        });\n        originalObj = Object.getPrototypeOf(originalObj);\n      }\n    }\n\n    return methods.sort();\n  }\n\n  /**\n   * 获取代理对象\n   */\n  public getProxy(): any {\n    return this.proxy;\n  }\n\n  /**\n   * 获取原始地图实例\n   */\n  public getMapInstance(): IMapInstance {\n    return this.mapInstance;\n  }\n\n  /**\n   * 销毁代理\n   */\n  public destroy(): void {\n    if (this.mapInstance) {\n      this.mapInstance.remove();\n    }\n    this.mapInstance = null as any;\n    this.proxy = null;\n  }\n}\n", "/**\n * 统一地图SDK类型定义\n */\n\n// 地图类型枚举\nexport enum MapType {\n  MAP_2D = '2d',\n  MAP_3D = '3d'\n}\n\n// 基础配置接口\nexport interface BaseMapConfig {\n  container: string | HTMLElement;\n  center?: [number, number];\n  zoom?: number;\n  bearing?: number;\n  pitch?: number;\n  style?: string | object;\n}\n\n// 2D地图配置\nexport interface Map2DConfig extends BaseMapConfig {\n  type: MapType.MAP_2D;\n  // MapboxGL特有配置\n  accessToken?: string;\n  hash?: boolean;\n  interactive?: boolean;\n  bearingSnap?: number;\n  pitchWithRotate?: boolean;\n  clickTolerance?: number;\n  attributionControl?: boolean;\n  customAttribution?: string | string[];\n  logoPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';\n  failIfMajorPerformanceCaveat?: boolean;\n  preserveDrawingBuffer?: boolean;\n  antialias?: boolean;\n  refreshExpiredTiles?: boolean;\n  maxBounds?: [[number, number], [number, number]];\n  scrollZoom?: boolean;\n  boxZoom?: boolean;\n  dragRotate?: boolean;\n  dragPan?: boolean;\n  keyboard?: boolean;\n  doubleClickZoom?: boolean;\n  touchZoomRotate?: boolean;\n  touchPitch?: boolean;\n  trackResize?: boolean;\n  renderWorldCopies?: boolean;\n  maxTileCacheSize?: number;\n  localIdeographFontFamily?: string;\n  transformRequest?: (url: string, resourceType: string) => any;\n  collectResourceTiming?: boolean;\n  fadeDuration?: number;\n  crossSourceCollisions?: boolean;\n}\n\n// 3D场景配置\nexport interface Map3DConfig extends BaseMapConfig {\n  type: MapType.MAP_3D;\n  // SuperMap3D特有配置\n  scene3DOnly?: boolean;\n  shadows?: boolean;\n  terrainShadows?: number;\n  mapMode2D?: number;\n  projectionPicker?: boolean;\n  baseLayerPicker?: boolean;\n  geocoder?: boolean;\n  homeButton?: boolean;\n  sceneModePicker?: boolean;\n  navigationHelpButton?: boolean;\n  animation?: boolean;\n  timeline?: boolean;\n  fullscreenButton?: boolean;\n  vrButton?: boolean;\n  globe?: boolean;\n  skyBox?: boolean;\n  skyAtmosphere?: boolean;\n  orderIndependentTranslucency?: boolean;\n  contextOptions?: {\n    webgl?: object;\n    allowTextureFilterAnisotropic?: boolean;\n  };\n  sceneMode?: number;\n  mapProjection?: any;\n  dataSources?: any[];\n  terrainProvider?: any;\n  imageryProvider?: any;\n  clockViewModel?: any;\n  selectedImageryProviderViewModel?: any;\n  selectedTerrainProviderViewModel?: any;\n  imageryProviderViewModels?: any[];\n  terrainProviderViewModels?: any[];\n  skyBoxViewModel?: any;\n  fullscreenElement?: Element | string;\n  useDefaultRenderLoop?: boolean;\n  targetFrameRate?: number;\n  showRenderLoopErrors?: boolean;\n  automaticallyTrackDataSourceClocks?: boolean;\n  contextOptions2D?: object;\n  sceneOptions?: object;\n}\n\n// 统一配置类型\nexport type MapConfig = Map2DConfig | Map3DConfig;\n\n// 图层接口\nexport interface LayerConfig {\n  id: string;\n  type: string;\n  source?: string | object;\n  layout?: object;\n  paint?: object;\n  filter?: any[];\n  minzoom?: number;\n  maxzoom?: number;\n  metadata?: object;\n}\n\n// 数据源接口\nexport interface SourceConfig {\n  type: string;\n  url?: string;\n  tiles?: string[];\n  data?: string | object;\n  scheme?: string;\n  minzoom?: number;\n  maxzoom?: number;\n  attribution?: string;\n  promoteId?: string | object;\n  volatile?: boolean;\n}\n\n// 事件类型\nexport interface MapEvent {\n  type: string;\n  target: any;\n  originalEvent?: Event;\n  point?: [number, number];\n  lngLat?: [number, number];\n  features?: any[];\n}\n\n// 地图实例接口\nexport interface IMapInstance {\n  // 基础方法\n  getContainer(): HTMLElement;\n  getCenter(): [number, number];\n  setCenter(center: [number, number]): this;\n  getZoom(): number;\n  setZoom(zoom: number): this;\n  getBearing(): number;\n  setBearing(bearing: number): this;\n  getPitch(): number;\n  setPitch(pitch: number): this;\n  \n  // 图层管理\n  addLayer(layer: LayerConfig): this;\n  removeLayer(layerId: string): this;\n  getLayer(layerId: string): any;\n  moveLayer(layerId: string, beforeId?: string): this;\n  \n  // 数据源管理\n  addSource(sourceId: string, source: SourceConfig): this;\n  removeSource(sourceId: string): this;\n  getSource(sourceId: string): any;\n  \n  // 事件管理\n  on(type: string, listener: (event: MapEvent) => void): this;\n  off(type: string, listener?: (event: MapEvent) => void): this;\n  fire(type: string, data?: any): this;\n  \n  // 控制方法\n  resize(): this;\n  remove(): void;\n  \n  // 获取原始实例\n  getOriginalInstance(): any;\n}\n\n// 错误类型\nexport class MapSDKError extends Error {\n  constructor(message: string, public code?: string) {\n    super(message);\n    this.name = 'MapSDKError';\n  }\n}\n\n// 工厂配置\nexport interface FactoryConfig {\n  // SDK路径配置\n  mapboxgl?: {\n    js: string;\n    css: string;\n  };\n  supermap3d?: {\n    js: string;\n    css?: string;\n  };\n  iclientMapboxgl?: {\n    js: string;\n    css: string;\n  };\n  iclient3d?: {\n    js: string;\n  };\n}\n\n// 加载状态\nexport interface LoadingState {\n  mapboxgl?: boolean;\n  supermap3d?: boolean;\n  iclientMapboxgl?: boolean;\n  iclient3d?: boolean;\n}\n", "/**\n * 动态加载器 - 负责按需加载SDK资源\n */\n\nimport { FactoryConfig, LoadingState, MapSDKError } from '../types';\n\nexport class ResourceLoader {\n  private static instance: ResourceLoader;\n  private loadingState: LoadingState = {};\n  private loadingPromises: Map<string, Promise<void>> = new Map();\n  private config: FactoryConfig = {};\n\n  private constructor() {}\n\n  public static getInstance(): ResourceLoader {\n    if (!ResourceLoader.instance) {\n      ResourceLoader.instance = new ResourceLoader();\n    }\n    return ResourceLoader.instance;\n  }\n\n  /**\n   * 设置SDK资源配置\n   */\n  public setConfig(config: FactoryConfig): void {\n    this.config = { ...this.config, ...config };\n  }\n\n  /**\n   * 加载CSS文件\n   */\n  private loadCSS(url: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      // 检查是否已经加载\n      const existingLink = document.querySelector(`link[href=\"${url}\"]`);\n      if (existingLink) {\n        resolve();\n        return;\n      }\n\n      const link = document.createElement('link');\n      link.rel = 'stylesheet';\n      link.href = url;\n      \n      link.onload = () => resolve();\n      link.onerror = () => reject(new MapSDKError(`Failed to load CSS: ${url}`));\n      \n      document.head.appendChild(link);\n    });\n  }\n\n  /**\n   * 加载JavaScript文件\n   */\n  private loadJS(url: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      // 检查是否已经加载\n      const existingScript = document.querySelector(`script[src=\"${url}\"]`);\n      if (existingScript) {\n        resolve();\n        return;\n      }\n\n      const script = document.createElement('script');\n      script.src = url;\n      script.type = 'text/javascript';\n      \n      script.onload = () => resolve();\n      script.onerror = () => reject(new MapSDKError(`Failed to load JS: ${url}`));\n      \n      document.head.appendChild(script);\n    });\n  }\n\n  /**\n   * 加载MapboxGL\n   */\n  public async loadMapboxGL(): Promise<void> {\n    if (this.loadingState.mapboxgl) {\n      return;\n    }\n\n    const cacheKey = 'mapboxgl';\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadPromise = this._loadMapboxGL();\n    this.loadingPromises.set(cacheKey, loadPromise);\n    \n    try {\n      await loadPromise;\n      this.loadingState.mapboxgl = true;\n    } catch (error) {\n      this.loadingPromises.delete(cacheKey);\n      throw error;\n    }\n  }\n\n  private async _loadMapboxGL(): Promise<void> {\n    const config = this.config.mapboxgl;\n    if (!config) {\n      throw new MapSDKError('MapboxGL configuration not found');\n    }\n\n    // 检查全局对象是否已存在\n    if (typeof (window as any).mapboxgl !== 'undefined') {\n      return;\n    }\n\n    // 并行加载CSS和JS\n    await Promise.all([\n      config.css ? this.loadCSS(config.css) : Promise.resolve(),\n      this.loadJS(config.js)\n    ]);\n\n    // 验证加载结果\n    if (typeof (window as any).mapboxgl === 'undefined') {\n      throw new MapSDKError('MapboxGL failed to load properly');\n    }\n  }\n\n  /**\n   * 加载SuperMap3D\n   */\n  public async loadSuperMap3D(): Promise<void> {\n    if (this.loadingState.supermap3d) {\n      return;\n    }\n\n    const cacheKey = 'supermap3d';\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadPromise = this._loadSuperMap3D();\n    this.loadingPromises.set(cacheKey, loadPromise);\n    \n    try {\n      await loadPromise;\n      this.loadingState.supermap3d = true;\n    } catch (error) {\n      this.loadingPromises.delete(cacheKey);\n      throw error;\n    }\n  }\n\n  private async _loadSuperMap3D(): Promise<void> {\n    const config = this.config.supermap3d;\n    if (!config) {\n      throw new MapSDKError('SuperMap3D configuration not found');\n    }\n\n    // 检查全局对象是否已存在\n    if (typeof (window as any).SuperMap3D !== 'undefined') {\n      return;\n    }\n\n    // 加载资源\n    const loadTasks = [this.loadJS(config.js)];\n    if (config.css) {\n      loadTasks.push(this.loadCSS(config.css));\n    }\n\n    await Promise.all(loadTasks);\n\n    // 验证加载结果\n    if (typeof (window as any).SuperMap3D === 'undefined') {\n      throw new MapSDKError('SuperMap3D failed to load properly');\n    }\n  }\n\n  /**\n   * 加载iClient for MapboxGL\n   */\n  public async loadIClientMapboxGL(): Promise<void> {\n    if (this.loadingState.iclientMapboxgl) {\n      return;\n    }\n\n    const cacheKey = 'iclientMapboxgl';\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadPromise = this._loadIClientMapboxGL();\n    this.loadingPromises.set(cacheKey, loadPromise);\n    \n    try {\n      await loadPromise;\n      this.loadingState.iclientMapboxgl = true;\n    } catch (error) {\n      this.loadingPromises.delete(cacheKey);\n      throw error;\n    }\n  }\n\n  private async _loadIClientMapboxGL(): Promise<void> {\n    const config = this.config.iclientMapboxgl;\n    if (!config) {\n      throw new MapSDKError('iClient MapboxGL configuration not found');\n    }\n\n    // 先确保MapboxGL已加载\n    await this.loadMapboxGL();\n\n    // 检查iClient是否已加载\n    const mapboxgl = (window as any).mapboxgl;\n    if (mapboxgl && mapboxgl.supermap) {\n      return;\n    }\n\n    // 并行加载CSS和JS\n    await Promise.all([\n      this.loadCSS(config.css),\n      this.loadJS(config.js)\n    ]);\n\n    // 验证加载结果\n    if (!mapboxgl || !mapboxgl.supermap) {\n      throw new MapSDKError('iClient for MapboxGL failed to load properly');\n    }\n  }\n\n  /**\n   * 加载iClient3D\n   */\n  public async loadIClient3D(): Promise<void> {\n    if (this.loadingState.iclient3d) {\n      return;\n    }\n\n    const cacheKey = 'iclient3d';\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadPromise = this._loadIClient3D();\n    this.loadingPromises.set(cacheKey, loadPromise);\n    \n    try {\n      await loadPromise;\n      this.loadingState.iclient3d = true;\n    } catch (error) {\n      this.loadingPromises.delete(cacheKey);\n      throw error;\n    }\n  }\n\n  private async _loadIClient3D(): Promise<void> {\n    const config = this.config.iclient3d;\n    if (!config) {\n      throw new MapSDKError('iClient3D configuration not found');\n    }\n\n    // 先确保SuperMap3D已加载\n    await this.loadSuperMap3D();\n\n    // 检查iClient3D是否已加载（通过检查SuperMap3D的扩展）\n    const SuperMap3D = (window as any).SuperMap3D;\n    if (SuperMap3D && SuperMap3D.iClient) {\n      return;\n    }\n\n    // 加载iClient3D\n    await this.loadJS(config.js);\n\n    // 验证加载结果（这里可能需要根据实际的iClient3D结构调整）\n    if (!SuperMap3D || !SuperMap3D.iClient) {\n      console.warn('iClient3D validation may need adjustment based on actual structure');\n    }\n  }\n\n  /**\n   * 获取加载状态\n   */\n  public getLoadingState(): LoadingState {\n    return { ...this.loadingState };\n  }\n\n  /**\n   * 重置加载状态（用于测试）\n   */\n  public reset(): void {\n    this.loadingState = {};\n    this.loadingPromises.clear();\n  }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "/**\n * 统一地图SDK主入口\n */\n\nimport { MapFactory } from './core/map-factory';\nimport { MapProxy } from './core/map-proxy';\nimport { ResourceLoader } from './utils/loader';\nimport {\n  MapConfig,\n  MapType,\n  Map2DConfig,\n  Map3DConfig,\n  IMapInstance,\n  FactoryConfig,\n  LayerConfig,\n  SourceConfig,\n  MapEvent,\n  MapSDKError\n} from './types';\n\n/**\n * 统一地图SDK类\n */\nexport class SGMapSDK {\n  private static factory: MapFactory = MapFactory.getInstance();\n  private static loader: ResourceLoader = ResourceLoader.getInstance();\n\n  /**\n   * 设置SDK资源配置\n   */\n  public static setConfig(config: Partial<FactoryConfig>): void {\n    this.factory.setConfig(config);\n  }\n\n  /**\n   * 创建地图实例\n   */\n  public static async createMap(config: MapConfig): Promise<any> {\n    try {\n      const mapInstance = await this.factory.createMap(config);\n      const proxy = new MapProxy(mapInstance);\n      return proxy.getProxy();\n    } catch (error) {\n      if (error instanceof MapSDKError) {\n        throw error;\n      }\n      throw new MapSDKError(`Failed to create map: ${error.message}`);\n    }\n  }\n\n  /**\n   * 获取加载状态\n   */\n  public static getLoadingState() {\n    return this.factory.getLoadingState();\n  }\n\n  /**\n   * 预加载SDK资源\n   */\n  public static async preloadSDK(type: 'all' | '2d' | '3d' = 'all'): Promise<void> {\n    const loadTasks: Promise<void>[] = [];\n\n    if (type === 'all' || type === '2d') {\n      loadTasks.push(\n        this.loader.loadMapboxGL(),\n        this.loader.loadIClientMapboxGL()\n      );\n    }\n\n    if (type === 'all' || type === '3d') {\n      loadTasks.push(\n        this.loader.loadSuperMap3D(),\n        this.loader.loadIClient3D()\n      );\n    }\n\n    await Promise.all(loadTasks);\n  }\n\n  /**\n   * 检查SDK是否已加载\n   */\n  public static isSDKLoaded(type: '2d' | '3d'): boolean {\n    const state = this.getLoadingState();\n\n    if (type === '2d') {\n      return !!(state.mapboxgl && state.iclientMapboxgl);\n    } else {\n      return !!(state.supermap3d && state.iclient3d);\n    }\n  }\n\n  /**\n   * 获取版本信息\n   */\n  public static getVersion(): string {\n    return '1.0.0';\n  }\n}\n\n/**\n * 便捷的创建地图函数\n */\nexport async function createMap(config: MapConfig): Promise<any> {\n  return SGMapSDK.createMap(config);\n}\n\n/**\n * 便捷的2D地图创建函数\n */\nexport async function create2DMap(config: Omit<Map2DConfig, 'type'>): Promise<any> {\n  return SGMapSDK.createMap({ ...config, type: MapType.MAP_2D });\n}\n\n/**\n * 便捷的3D地图创建函数\n */\nexport async function create3DMap(config: Omit<Map3DConfig, 'type'>): Promise<any> {\n  return SGMapSDK.createMap({ ...config, type: MapType.MAP_3D });\n}\n\n/**\n * 自动检测地图类型并创建\n */\nexport async function createAutoMap(config: Omit<MapConfig, 'type'>): Promise<any> {\n  return SGMapSDK.createMap(config as MapConfig);\n}\n\n// 导出类型和接口\nexport {\n  MapType,\n  MapConfig,\n  Map2DConfig,\n  Map3DConfig,\n  IMapInstance,\n  FactoryConfig,\n  LayerConfig,\n  SourceConfig,\n  MapEvent,\n  MapSDKError\n};\n\n// 导出核心类\nexport { MapFactory, MapProxy, ResourceLoader };\n\n// 默认导出\nexport default SGMapSDK;\n\n// 全局对象挂载（用于script标签引入）\nif (typeof window !== 'undefined') {\n  (window as any).SGMapSDK = SGMapSDK;\n  (window as any).createMap = createMap;\n  (window as any).create2DMap = create2DMap;\n  (window as any).create3DMap = create3DMap;\n  (window as any).createAutoMap = createAutoMap;\n}\n"], "names": ["MapSDKError", "BaseMapAdapter", "instance", "container", "_classCallCheck", "_defineProperty", "Map", "setupEventForwarding", "_createClass", "key", "value", "normalizeEvent", "originalEvent", "type", "target", "point", "lngLat", "features", "getContainer", "on", "listener", "eventListeners", "has", "set", "Set", "get", "add", "off", "listeners", "delete", "clear", "size", "fire", "data", "event", "_objectSpread", "for<PERSON>ach", "error", "console", "concat", "getOriginalInstance", "validateLayerConfig", "layer", "id", "validateSourceConfig", "source", "safeExecute", "fn", "errorMessage", "message", "checkInstance", "MapboxAdapter", "_BaseMapAdapter", "_callSuper", "arguments", "_inherits", "_this", "events", "eventType", "e", "normalizedEvent", "getCenter", "center", "lng", "lat", "setCenter", "_this2", "getZoom", "setZoom", "zoom", "_this3", "getBearing", "setBearing", "bearing", "_this4", "get<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pitch", "_this5", "add<PERSON><PERSON>er", "_this6", "<PERSON><PERSON><PERSON>er", "layerId", "_this7", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "beforeId", "_this8", "addSource", "sourceId", "_this9", "removeSource", "_this0", "getSource", "resize", "_this1", "remove", "_this10", "setStyle", "style", "_this11", "getStyle", "flyTo", "options", "_this12", "jumpTo", "_this13", "fitBounds", "bounds", "_this14", "queryRenderedFeatures", "pointOrBox", "_this15", "querySourceFeatures", "_this16", "unproject", "project", "x", "y", "SuperMap3DAdapter", "scene", "camera", "canvas", "handler", "window", "SuperMap3D", "ScreenSpaceEventHandler", "setInputAction", "pickedPosition", "pickPosition", "position", "cartographic", "Cartographic", "fromCartesian", "longitude", "Math", "toDegrees", "latitude", "ScreenSpaceEventType", "LEFT_CLICK", "endPosition", "MOUSE_MOVE", "moveStart", "addEventListener", "moveEnd", "postRender", "destination", "Cartesian3", "fromDegrees", "<PERSON><PERSON><PERSON><PERSON>", "height", "log2", "pow", "heading", "toRadians", "orientation", "roll", "pitchRadians", "addImageryLayer", "addS3MLayer", "addTerrainLayer", "addMVTLayer", "warn", "imageryLayers", "_typeof", "url", "provider", "SuperMapImageryProvider", "addImageryProvider", "addS3MTilesLayerByScp", "name", "terrainProvider", "SuperMapTerrainProvider", "isSct", "addVectorTilesMap", "viewer", "i", "length", "layers", "removeAll", "destroy", "getScene", "getCamera", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "G", "v", "a", "d", "bind", "l", "TypeError", "call", "done", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "enumerable", "configurable", "writable", "_invoke", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "apply", "_next", "_throw", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "push", "getOwnPropertyDescriptors", "defineProperties", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "MapType", "Resource<PERSON><PERSON>der", "MapFactory", "mapboxgl", "js", "css", "supermap3d", "iclientMapboxgl", "iclient3d", "loader", "getInstance", "setConfig", "defaultConfig", "config", "_createMap", "_callee", "_context", "validateConfig", "resolveContainer", "MAP_2D", "create2DMap", "MAP_3D", "create3DMap", "autoDetectMapType", "createMap", "_x", "_autoDetectMapType", "_callee2", "has3DFeatures", "config3D", "config2D", "_context2", "detect3DFeatures", "log", "_x2", "_x3", "has3DConfig", "scene3DOnly", "shadows", "globe", "skyBox", "sceneMode", "has3DStyle", "some", "_create2DMap", "_callee3", "mapConfig", "mapInstance", "_context3", "all", "loadMapboxGL", "loadIClientMapboxGL", "prepare2DConfig", "reject", "_e$error", "setTimeout", "_x4", "_x5", "_create3DMap", "_callee4", "viewerConfig", "_context4", "loadSuperMap3D", "loadIClient3D", "prepare3DConfig", "Viewer", "zoomToHeight", "_x6", "_x7", "mapboxConfig", "_objectWithoutProperties", "_excluded", "_excluded2", "animation", "timeline", "baseLayerPicker", "fullscreenButton", "geocoder", "homeButton", "sceneModePicker", "navigationHelpButton", "element", "document", "getElementById", "querySelector", "values", "includes", "getLoadingState", "MapProxy", "proxy", "createProxy", "Proxy", "property", "receiver", "Reflect", "originalInstance", "handleSpecialProperties", "getOwnPropertyNames", "Array", "from", "descriptor", "undefined", "getVersion", "getMapType", "isLoaded", "getAvailableMethods", "join", "version", "VERSION", "loaded", "methods", "obj", "originalObj", "sort", "getProxy", "getMapInstance", "_Error", "code", "_wrapNativeSuper", "Error", "loadCSS", "existingLink", "link", "createElement", "rel", "href", "onload", "onerror", "head", "append<PERSON><PERSON><PERSON>", "loadJS", "existingScript", "script", "src", "_loadMapboxGL2", "cache<PERSON>ey", "loadPromise", "_t", "loadingState", "loadingPromises", "_loadMapboxGL", "_loadMapboxGL3", "_loadSuperMap3D2", "_t2", "_loadSuperMap3D", "_loadSuperMap3D3", "loadTasks", "_loadIClientMapboxGL2", "_callee5", "_t3", "_context5", "_loadIClientMapboxGL", "_loadIClientMapboxGL3", "_callee6", "_context6", "supermap", "_loadIClient3D2", "_callee7", "_t4", "_context7", "_loadIClient3D", "_loadIClient3D3", "_callee8", "_context8", "iClient", "reset", "MapConfig", "Map2DConfig", "Map3DConfig", "IMapInstance", "FactoryConfig", "LayerConfig", "SourceConfig", "MapEvent", "SGMapSDK", "factory", "_preloadSDK", "_args2", "preloadSDK", "isSDKLoaded", "state", "_createMap2", "createAutoMap", "_createAutoMap"], "sourceRoot": ""}