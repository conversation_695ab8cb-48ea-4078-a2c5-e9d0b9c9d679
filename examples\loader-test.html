<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SGMap Loader 测试 - 仿照高德地图 API</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        #map {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px 0;
        }
        
        .controls {
            margin: 10px 0;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #005a87;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        
        .code-example {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>SGMap Loader 测试</h1>
    <p>仿照高德地图 JS API Loader 的使用方式</p>
    
    <div class="code-example">
        <strong>使用方式（类似高德地图）：</strong><br>
        <code>
// 高德地图方式<br>
import AMapLoader from '@amap/amap-jsapi-loader';<br>
AMapLoader.load({ version: '2.0', plugins: ['AMap.Scale'] })<br>
&nbsp;&nbsp;.then((AMap) => { /* 使用 AMap */ });<br><br>

// 我们的 SGMap 方式<br>
import { load } from 'sg-map-sdk';<br>
load({ version: '1.0.0', type: '2d' })<br>
&nbsp;&nbsp;.then((SGMap) => { /* 使用 SGMap */ });
        </code>
    </div>
    
    <div class="controls">
        <button id="loadAll">加载完整 SDK</button>
        <button id="load2D">仅加载 2D SDK</button>
        <button id="load3D">仅加载 3D SDK</button>
        <button id="createMap" disabled>创建地图</button>
        <button id="clear">清除地图</button>
        <button id="clearLog">清除日志</button>
    </div>
    
    <div id="map"></div>
    
    <div class="log" id="log"></div>

    <!-- 加载统一地图SDK (调试版本) -->
    <script src="../dist/sg-map-sdk.debug.js"></script>
    
    <script>
        let currentMap = null;
        let loadedSDK = null;
        
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logEl.innerHTML += `<span class="${className}">[${time}] ${message}</span>\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[${time}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function clearMap() {
            if (currentMap) {
                currentMap.remove();
                currentMap = null;
                log('✓ 地图已清除', 'success');
            } else {
                log('没有地图需要清除');
            }
        }
        
        // 加载完整 SDK
        async function loadAllSDK() {
            log('=== 开始加载完整 SDK ===');
            
            try {
                const startTime = Date.now();
                
                // 仿照高德地图的 load 方式
                loadedSDK = await window.load({
                    version: '1.0.0',
                    type: 'all',
                    debug: true,
                    paths: {
                        mapboxgl: {
                            js: '../iclient-mapboxgl/mapbox-gl.js',
                            css: '../iclient-mapboxgl/mapbox-gl.css'
                        },
                        iclientMapboxgl: {
                            js: '../iclient-mapboxgl/iclient-mapboxgl.js',
                            css: '../iclient-mapboxgl/iclient-mapboxgl.css'
                        },
                        supermap3d: {
                            js: '../iclient-3d/SuperMap3D/SuperMap3D.js',
                            css: '../iclient-3d/SuperMap3D/Widgets/widgets.css'
                        },
                        iclient3d: {
                            js: '../iclient-3d/deps.js'
                        }
                    }
                });
                
                const endTime = Date.now();
                
                log(`✓ 完整 SDK 加载成功，耗时: ${endTime - startTime}ms`, 'success');
                log(`SDK 版本: ${loadedSDK.getVersion()}`);
                log(`SDK 类型: ${typeof loadedSDK}`);
                
                // 启用创建地图按钮
                document.getElementById('createMap').disabled = false;
                
            } catch (error) {
                log(`✗ SDK 加载失败: ${error.message}`, 'error');
                console.error(error);
            }
            
            log('=== 完整 SDK 加载完成 ===');
        }
        
        // 仅加载 2D SDK
        async function load2DSDK() {
            log('=== 开始加载 2D SDK ===');
            
            try {
                const startTime = Date.now();
                
                loadedSDK = await window.load({
                    version: '1.0.0',
                    type: '2d',
                    debug: true,
                    paths: {
                        mapboxgl: {
                            js: '../iclient-mapboxgl/mapbox-gl.js',
                            css: '../iclient-mapboxgl/mapbox-gl.css'
                        },
                        iclientMapboxgl: {
                            js: '../iclient-mapboxgl/iclient-mapboxgl.js',
                            css: '../iclient-mapboxgl/iclient-mapboxgl.css'
                        }
                    }
                });
                
                const endTime = Date.now();
                
                log(`✓ 2D SDK 加载成功，耗时: ${endTime - startTime}ms`, 'success');
                log(`SDK 版本: ${loadedSDK.getVersion()}`);
                
                // 启用创建地图按钮
                document.getElementById('createMap').disabled = false;
                
            } catch (error) {
                log(`✗ 2D SDK 加载失败: ${error.message}`, 'error');
                console.error(error);
            }
            
            log('=== 2D SDK 加载完成 ===');
        }
        
        // 仅加载 3D SDK
        async function load3DSDK() {
            log('=== 开始加载 3D SDK ===');
            
            try {
                const startTime = Date.now();
                
                loadedSDK = await window.load({
                    version: '1.0.0',
                    type: '3d',
                    debug: true,
                    paths: {
                        supermap3d: {
                            js: '../iclient-3d/SuperMap3D/SuperMap3D.js',
                            css: '../iclient-3d/SuperMap3D/Widgets/widgets.css'
                        },
                        iclient3d: {
                            js: '../iclient-3d/deps.js'
                        }
                    }
                });
                
                const endTime = Date.now();
                
                log(`✓ 3D SDK 加载成功，耗时: ${endTime - startTime}ms`, 'success');
                log(`SDK 版本: ${loadedSDK.getVersion()}`);
                
                // 启用创建地图按钮
                document.getElementById('createMap').disabled = false;
                
            } catch (error) {
                log(`✗ 3D SDK 加载失败: ${error.message}`, 'error');
                console.error(error);
            }
            
            log('=== 3D SDK 加载完成 ===');
        }
        
        // 创建地图
        async function createMapInstance() {
            if (!loadedSDK) {
                log('请先加载 SDK', 'warning');
                return;
            }
            
            log('=== 开始创建地图 ===');
            
            try {
                const startTime = Date.now();
                
                // 清除现有地图
                if (currentMap) {
                    currentMap.remove();
                    currentMap = null;
                }
                
                const config = {
                    type: '2d',
                    container: 'map',
                    center: [116.3974, 39.9093],
                    zoom: 10,
                    style: {
                        version: 8,
                        sources: {
                            'osm-tiles': {
                                type: 'raster',
                                tiles: [
                                    'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
                                ],
                                tileSize: 256,
                                attribution: '© OpenStreetMap contributors'
                            }
                        },
                        layers: [{
                            id: 'osm-tiles-layer',
                            type: 'raster',
                            source: 'osm-tiles',
                            minzoom: 0,
                            maxzoom: 19
                        }]
                    }
                };
                
                log('使用已加载的 SDK 创建地图...');
                
                // 使用加载的 SDK 创建地图
                currentMap = await loadedSDK.createMap(config);
                
                const endTime = Date.now();
                
                log(`✓ 地图创建成功，耗时: ${endTime - startTime}ms`, 'success');
                log(`地图中心: ${JSON.stringify(currentMap.getCenter())}`);
                log(`缩放级别: ${currentMap.getZoom()}`);
                
            } catch (error) {
                log(`✗ 地图创建失败: ${error.message}`, 'error');
                console.error(error);
            }
            
            log('=== 地图创建完成 ===');
        }
        
        // 绑定事件
        document.getElementById('loadAll').addEventListener('click', loadAllSDK);
        document.getElementById('load2D').addEventListener('click', load2DSDK);
        document.getElementById('load3D').addEventListener('click', load3DSDK);
        document.getElementById('createMap').addEventListener('click', createMapInstance);
        document.getElementById('clear').addEventListener('click', clearMap);
        document.getElementById('clearLog').addEventListener('click', clearLog);
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            if (typeof window.load !== 'undefined') {
                log('✓ SGMapLoader 已加载', 'success');
            } else {
                log('✗ SGMapLoader 未加载', 'error');
            }
            
            log('请选择加载方式：完整 SDK / 仅 2D / 仅 3D', 'warning');
        });
    </script>
</body>
</html>
