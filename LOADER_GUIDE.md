# SGMap Loader 使用指南

仿照高德地图 JS API Loader 的设计模式，提供更优雅的 SDK 加载和使用方式。

## 🎯 设计理念

### 高德地图 JS API Loader 模式
```javascript
import AMapLoader from '@amap/amap-jsapi-loader';

AMapLoader.load({
  version: '2.0',
  plugins: ['AMap.Scale', 'AMap.ToolBar']
}).then((AMap) => {
  // 使用 AMap 对象
  const map = new AMap.Map('container', {
    center: [116.397428, 39.90923],
    zoom: 11
  });
});
```

### 我们的 SGMap Loader 模式
```javascript
import { load } from 'sg-map-sdk';

load({
  version: '1.0.0',
  type: '2d',
  paths: { /* 资源路径配置 */ }
}).then((SGMap) => {
  // 使用 SGMap 对象
  const map = await SGMap.createMap({
    type: '2d',
    container: 'map',
    center: [116.397428, 39.90923],
    zoom: 11
  });
});
```

## 📋 API 对比

| 特性 | 高德地图 | SGMap |
|------|----------|-------|
| **加载方式** | `AMapLoader.load()` | `SGMapLoader.load()` 或 `load()` |
| **版本控制** | `version: '2.0'` | `version: '1.0.0'` |
| **插件系统** | `plugins: ['AMap.Scale']` | `plugins: ['custom-plugin']` |
| **按需加载** | 支持 | `type: '2d' \| '3d' \| 'all'` |
| **返回对象** | `AMap` 全局对象 | `SGMap` SDK 实例 |

## 🚀 使用方式

### 1. 基础用法

```javascript
// 加载完整 SDK
import { load } from 'sg-map-sdk';

const SGMap = await load({
  version: '1.0.0',
  type: 'all'
});

// 创建地图
const map = await SGMap.createMap({
  type: '2d',
  container: 'map',
  center: [116.3974, 39.9093],
  zoom: 10
});
```

### 2. 按需加载

```javascript
// 仅加载 2D 地图功能
const SGMap = await load({
  type: '2d',
  paths: {
    mapboxgl: {
      js: './libs/mapbox-gl.js',
      css: './libs/mapbox-gl.css'
    }
  }
});

// 仅加载 3D 地图功能
const SGMap = await load({
  type: '3d',
  paths: {
    supermap3d: {
      js: './libs/SuperMap3D.js',
      css: './libs/widgets.css'
    }
  }
});
```

### 3. 自定义配置

```javascript
const SGMap = await load({
  version: '1.0.0',
  type: 'all',
  debug: true,
  plugins: ['custom-plugin'],
  paths: {
    mapboxgl: {
      js: 'https://cdn.example.com/mapbox-gl.js',
      css: 'https://cdn.example.com/mapbox-gl.css'
    },
    iclientMapboxgl: {
      js: './libs/iclient-mapboxgl.js',
      css: './libs/iclient-mapboxgl.css'
    }
  }
});
```

## 🔧 配置选项

### SGMapLoaderConfig

```typescript
interface SGMapLoaderConfig {
  /** SDK 版本 */
  version?: string;
  
  /** 地图类型 */
  type?: 'all' | '2d' | '3d';
  
  /** 资源路径配置 */
  paths?: {
    mapboxgl?: { js: string; css: string };
    iclientMapboxgl?: { js: string; css: string };
    supermap3d?: { js: string; css: string };
    iclient3d?: { js: string };
  };
  
  /** 是否启用调试模式 */
  debug?: boolean;
  
  /** 自定义插件 */
  plugins?: string[];
}
```

## 🎨 优势特点

### 1. **按需加载**
- `type: '2d'` - 仅加载 2D 地图相关依赖
- `type: '3d'` - 仅加载 3D 地图相关依赖  
- `type: 'all'` - 加载完整功能

### 2. **缓存机制**
- 同一配置多次调用 `load()` 只会加载一次
- 自动缓存已加载的 SDK 实例

### 3. **错误处理**
- 详细的加载错误信息
- 依赖缺失检测
- 网络错误重试

### 4. **调试支持**
- `debug: true` 开启详细日志
- 加载时间统计
- 依赖状态检查

## 📝 迁移指南

### 从旧版本迁移

```javascript
// 旧版本方式
import { SGMapSDK, createMap } from 'sg-map-sdk';

SGMapSDK.setConfig({ /* 配置 */ });
await SGMapSDK.preloadDependencies();
const map = await createMap(config);

// 新版本方式（推荐）
import { load } from 'sg-map-sdk';

const SGMap = await load({
  type: '2d',
  paths: { /* 配置 */ }
});
const map = await SGMap.createMap(config);
```

### 兼容性

新版本完全兼容旧版本 API：

```javascript
// 这些方式仍然可用
import { SGMapSDK, createMap, preloadDependencies } from 'sg-map-sdk';

// 旧版本 API 继续工作
await preloadDependencies();
const map = await createMap(config);
```

## 🌟 最佳实践

### 1. 生产环境使用

```javascript
const SGMap = await load({
  version: '1.0.0',
  type: '2d',
  debug: false,
  paths: {
    mapboxgl: {
      js: 'https://cdn.jsdelivr.net/npm/mapbox-gl@1.13.2/dist/mapbox-gl.js',
      css: 'https://cdn.jsdelivr.net/npm/mapbox-gl@1.13.2/dist/mapbox-gl.css'
    }
  }
});
```

### 2. 开发环境使用

```javascript
const SGMap = await load({
  type: 'all',
  debug: true,
  paths: {
    mapboxgl: { js: './libs/mapbox-gl.js', css: './libs/mapbox-gl.css' },
    iclientMapboxgl: { js: './libs/iclient-mapboxgl.js', css: './libs/iclient-mapboxgl.css' }
  }
});
```

### 3. 错误处理

```javascript
try {
  const SGMap = await load(config);
  const map = await SGMap.createMap(mapConfig);
} catch (error) {
  console.error('地图加载失败:', error.message);
  // 降级处理或错误提示
}
```

## 🔗 示例页面

- `examples/loader-test.html` - Loader 模式完整示例
- `examples/sync-test.html` - 同步创建地图示例
- `examples/minimal-test.html` - 最小化测试示例
