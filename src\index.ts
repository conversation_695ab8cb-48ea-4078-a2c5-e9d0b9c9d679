/**
 * 统一地图SDK主入口
 */

import { MapFactory } from './core/map-factory';
import { MapProxy } from './core/map-proxy';
import { ResourceLoader } from './utils/loader';
import {
  MapConfig,
  MapType,
  Map2DConfig,
  Map3DConfig,
  IMapInstance,
  FactoryConfig,
  LayerConfig,
  SourceConfig,
  MapEvent,
  MapSDKError
} from './types';

/**
 * SDK Loader 配置接口
 */
export interface SGMapLoaderConfig {
  /** SDK 版本 */
  version?: string;
  /** 地图类型 */
  type?: 'all' | '2d' | '3d';
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 自定义插件 */
  plugins?: string[];
}

/**
 * 统一地图SDK Loader
 */
export class SGMapLoader {
  private static loadPromise: Promise<any> | null = null;
  private static loadedSDK: any = null;

  /**
   * 加载 SDK
   * @param config 加载配置
   * @returns Promise<SGMapSDK>
   */
  public static async load(config: SGMapLoaderConfig = {}): Promise<typeof SGMapSDK> {
    // 如果已经加载过，直接返回
    if (this.loadedSDK) {
      return this.loadedSDK;
    }

    // 如果正在加载，返回加载 Promise
    if (this.loadPromise) {
      return this.loadPromise;
    }

    // 开始加载
    this.loadPromise = this.performLoad(config);
    this.loadedSDK = await this.loadPromise;
    return this.loadedSDK;
  }

  /**
   * 执行实际的加载过程
   */
  private static async performLoad(config: SGMapLoaderConfig): Promise<typeof SGMapSDK> {
    const {
      version = '1.0.0',
      type = 'all',
      debug = false,
      plugins = []
    } = config;

    if (debug) {
      console.log(`[SGMapLoader] Loading SDK version ${version}, type: ${type}`);
    }

    // 设置固定的资源路径（内部配置）
    SGMapSDK.setConfig({
      mapboxgl: {
        js: './iclient-mapboxgl/mapbox-gl.js',
        css: './iclient-mapboxgl/mapbox-gl.css'
      },
      iclientMapboxgl: {
        js: './iclient-mapboxgl/iclient-mapboxgl.js',
        css: './iclient-mapboxgl/iclient-mapboxgl.css'
      },
      supermap3d: {
        js: './iclient-3d/SuperMap3D/SuperMap3D.js',
        css: './iclient-3d/SuperMap3D/Widgets/widgets.css'
      },
      iclient3d: {
        js: './iclient-3d/deps.js'
      }
    });

    if (debug) {
      console.log('[SGMapLoader] Resource paths configured');
    }

    // 根据类型预加载对应的依赖
    if (type === 'all') {
      await SGMapSDK.preloadDependencies();
    } else {
      await SGMapSDK.preloadSDK(type);
    }

    // 加载插件
    if (plugins.length > 0) {
      await this.loadPlugins(plugins);
    }

    if (debug) {
      console.log('[SGMapLoader] SDK loaded successfully');
    }

    return SGMapSDK;
  }

  /**
   * 加载插件
   */
  private static async loadPlugins(plugins: string[]): Promise<void> {
    // 这里可以扩展插件加载逻辑
    console.log('[SGMapLoader] Loading plugins:', plugins);
  }

  /**
   * 重置加载状态（用于测试）
   */
  public static reset(): void {
    this.loadPromise = null;
    this.loadedSDK = null;
  }
}

/**
 * 统一地图SDK类
 */
export class SGMapSDK {
  private static factory: MapFactory = MapFactory.getInstance();
  private static loader: ResourceLoader = ResourceLoader.getInstance();

  /**
   * 设置SDK资源配置
   */
  public static setConfig(config: Partial<FactoryConfig>): void {
    this.factory.setConfig(config);
  }

  /**
   * 预加载所有依赖
   */
  public static async preloadDependencies(): Promise<void> {
    return this.factory.preloadDependencies();
  }

  /**
   * 创建地图实例
   */
  public static createMap(config: MapConfig): any {
    try {
      const mapInstance = this.factory.createMap(config);
      const proxy = new MapProxy(mapInstance);
      return proxy.getProxy();
    } catch (error) {
      if (error instanceof MapSDKError) {
        throw error;
      }
      throw new MapSDKError(`Failed to create map: ${(error as Error).message}`);
    }
  }

  /**
   * 获取加载状态
   */
  public static getLoadingState() {
    return this.factory.getLoadingState();
  }

  /**
   * 预加载SDK资源
   */
  public static async preloadSDK(type: 'all' | '2d' | '3d' = 'all'): Promise<void> {
    const loadTasks: Promise<void>[] = [];

    if (type === 'all' || type === '2d') {
      loadTasks.push(
        this.loader.loadMapboxGL(),
        this.loader.loadIClientMapboxGL()
      );
    }

    if (type === 'all' || type === '3d') {
      loadTasks.push(
        this.loader.loadSuperMap3D(),
        this.loader.loadIClient3D()
      );
    }

    await Promise.all(loadTasks);
  }

  /**
   * 检查SDK是否已加载
   */
  public static isSDKLoaded(type: '2d' | '3d'): boolean {
    const state = this.getLoadingState();

    if (type === '2d') {
      return !!(state.mapboxgl && state.iclientMapboxgl);
    } else {
      return !!(state.supermap3d && state.iclient3d);
    }
  }

  /**
   * 获取版本信息
   */
  public static getVersion(): string {
    return '1.0.0';
  }
}

/**
 * 便捷的 Loader 函数
 */
export async function load(config: SGMapLoaderConfig = {}): Promise<typeof SGMapSDK> {
  return SGMapLoader.load(config);
}

/**
 * 预加载所有依赖（兼容旧版本）
 */
export async function preloadDependencies(): Promise<void> {
  return SGMapSDK.preloadDependencies();
}

/**
 * 便捷的创建地图函数
 */
export function createMap(config: MapConfig): any {
  return SGMapSDK.createMap(config);
}

/**
 * 便捷的2D地图创建函数
 */
export function create2DMap(config: Omit<Map2DConfig, 'type'>): any {
  return SGMapSDK.createMap({ ...config, type: MapType.MAP_2D });
}

/**
 * 便捷的3D地图创建函数
 */
export function create3DMap(config: Omit<Map3DConfig, 'type'>): any {
  return SGMapSDK.createMap({ ...config, type: MapType.MAP_3D });
}

/**
 * 自动检测地图类型并创建
 */
export function createAutoMap(config: Omit<MapConfig, 'type'>): any {
  return SGMapSDK.createMap(config as MapConfig);
}

// 导出类型和接口
export {
  MapType,
  MapConfig,
  Map2DConfig,
  Map3DConfig,
  IMapInstance,
  FactoryConfig,
  LayerConfig,
  SourceConfig,
  MapEvent,
  MapSDKError
};

// 导出核心类
export { MapFactory, MapProxy, ResourceLoader };

// 默认导出
export default SGMapSDK;

// 全局对象挂载（用于script标签引入）
if (typeof window !== 'undefined') {
  (window as any).SGMapSDK = SGMapSDK;
  (window as any).SGMapLoader = SGMapLoader;
  (window as any).load = load;
  (window as any).createMap = createMap;
  (window as any).preloadDependencies = preloadDependencies;
  (window as any).create2DMap = create2DMap;
  (window as any).create3DMap = create3DMap;
  (window as any).createAutoMap = createAutoMap;
}
