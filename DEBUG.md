# 调试指南

## 生成调试版本的 SDK

为了方便在 `examples/index.html` 中进行调试，我们提供了一个专门的调试构建配置。

### 1. 构建调试版本

```bash
npm run build:debug
```

这将生成以下文件：
- `dist/sg-map-sdk.debug.js` - 未压缩、未混淆的调试版本
- `dist/sg-map-sdk.debug.js.map` - Source Map 文件

### 2. 调试版本特点

- **未压缩**: 代码保持原始格式，易于阅读
- **未混淆**: 变量名和函数名保持原样
- **Source Map**: 提供完整的源码映射
- **详细错误信息**: 保留完整的错误堆栈信息
- **可读的模块名**: 使用命名模块 ID

### 3. 在示例中使用调试版本

示例文件已经配置为使用调试版本：

```html
<!-- examples/index.html -->
<script src="../dist/sg-map-sdk.debug.js"></script>

<!-- examples/simple-test.html -->
<script src="../dist/sg-map-sdk.debug.js"></script>
```

### 4. 调试技巧

#### 4.1 浏览器开发者工具
1. 打开浏览器开发者工具 (F12)
2. 在 Sources 面板中可以看到完整的源码结构
3. 可以在任何函数中设置断点
4. 查看 Console 面板获取详细的错误信息

#### 4.2 常见调试点
- **SDK 加载**: 检查 `window.SGMapSDK` 是否存在
- **配置设置**: 验证 `SGMapSDK.setConfig()` 调用
- **地图创建**: 在 `createMap` 函数中设置断点
- **事件处理**: 检查事件监听器的注册和触发

#### 4.3 错误排查
1. **SDK 未加载**: 检查脚本路径是否正确
2. **依赖缺失**: 查看网络面板确认所有资源加载成功
3. **配置错误**: 验证传递给 `createMap` 的配置对象
4. **API 调用失败**: 检查控制台的错误信息

### 5. 生产版本 vs 调试版本

| 特性 | 生产版本 (umd.js) | 调试版本 (debug.js) |
|------|------------------|-------------------|
| 文件大小 | 小 (~20KB) | 大 (~100KB) |
| 加载速度 | 快 | 慢 |
| 可读性 | 差 | 好 |
| 调试友好 | 差 | 好 |
| 错误信息 | 简化 | 详细 |

### 6. 切换版本

要在生产和调试版本之间切换，只需修改 HTML 文件中的脚本引用：

```html
<!-- 生产版本 -->
<script src="../dist/sg-map-sdk.umd.js"></script>

<!-- 调试版本 -->
<script src="../dist/sg-map-sdk.debug.js"></script>
```

### 7. 构建配置

调试版本的 webpack 配置位于 `webpack.debug.js`：

```javascript
module.exports = merge(common, {
  mode: 'development',
  devtool: 'source-map',
  optimization: {
    minimize: false,
    moduleIds: 'named',
    chunkIds: 'named',
    splitChunks: false,
    mangleExports: false,
  },
});
```

### 8. 注意事项

- 调试版本仅用于开发和调试，不要在生产环境中使用
- 调试版本文件较大，会影响加载性能
- 确保在发布前切换回生产版本
