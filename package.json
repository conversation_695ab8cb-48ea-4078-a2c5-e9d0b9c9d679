{"name": "sg-unified-map-sdk", "version": "1.0.0", "description": "统一的SuperMap地图SDK，整合iClient3D和iClient for MapboxGL", "main": "dist/sg-map-sdk.umd.js", "module": "dist/sg-map-sdk.esm.js", "types": "dist/types/index.d.ts", "files": ["dist", "src"], "scripts": {"dev": "webpack serve --config webpack.dev.js", "build": "npm run clean && npm run build:umd", "build:umd": "webpack --config webpack.umd.js", "clean": "node -e \"const fs = require('fs'); const path = require('path'); function rimraf(dir) { if (fs.existsSync(dir)) { const files = fs.readdirSync(dir); files.forEach(file => { const filePath = path.join(dir, file); if (fs.statSync(filePath).isDirectory()) { rimraf(filePath); } else { fs.unlinkSync(filePath); } }); fs.rmdirSync(dir); } } rimraf('dist');\"", "test": "echo \"Tests require Node.js >= 14\"", "lint": "echo \"Linting requires Node.js >= 14\"", "lint:fix": "echo \"Linting requires Node.js >= 14\""}, "keywords": ["supermap", "map", "3d", "mapbox", "gis", "sdk"], "author": "SuperMap SDK Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.12.0", "@babel/preset-env": "^7.12.0", "@babel/preset-typescript": "^7.12.0", "babel-loader": "^8.2.0", "css-loader": "^5.2.0", "html-webpack-plugin": "^4.5.0", "style-loader": "^2.0.0", "typescript": "^4.5.0", "webpack": "^5.50.0", "webpack-cli": "^4.8.0", "webpack-dev-server": "^3.11.0", "webpack-merge": "^5.8.0"}, "peerDependencies": {"mapbox-gl": "^1.13.0"}, "repository": {"type": "git", "url": "https://github.com/supermap/sg-unified-map-sdk.git"}, "bugs": {"url": "https://github.com/supermap/sg-unified-map-sdk/issues"}, "homepage": "https://github.com/supermap/sg-unified-map-sdk#readme"}