/**
 * 地图工厂 - 根据配置创建相应的地图实例
 */

import { MapConfig, MapType, Map2DConfig, Map3DConfig, IMapInstance, FactoryConfig, MapSDKError } from '../types';
import { ResourceLoader } from '../utils/loader';
import { MapboxAdapter } from '../adapters/mapbox-adapter';
import { SuperMap3DAdapter } from '../adapters/supermap3d-adapter';

export class MapFactory {
  private static instance: MapFactory;
  private loader: ResourceLoader;
  private defaultConfig: FactoryConfig = {
    mapboxgl: {
      js: 'https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.js',
      css: 'https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.css'
    },
    supermap3d: {
      js: './iclient-3d/SuperMap3D/SuperMap3D.js',
      css: './iclient-3d/SuperMap3D/Widgets/widgets.css'
    },
    iclientMapboxgl: {
      js: './iclient-mapboxgl/iclient-mapboxgl.js',
      css: './iclient-mapboxgl/iclient-mapboxgl.css'
    },
    iclient3d: {
      js: './iclient-3d/deps.js'
    }
  };

  private constructor() {
    this.loader = ResourceLoader.getInstance();
    this.loader.setConfig(this.defaultConfig);
  }

  public static getInstance(): MapFactory {
    if (!MapFactory.instance) {
      MapFactory.instance = new MapFactory();
    }
    return MapFactory.instance;
  }

  /**
   * 设置SDK资源配置
   */
  public setConfig(config: Partial<FactoryConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
    this.loader.setConfig(this.defaultConfig);
  }

  /**
   * 预加载所有依赖
   */
  public async preloadDependencies(): Promise<void> {
    await Promise.all([
      this.loader.loadMapboxGL(),
      this.loader.loadIClientMapboxGL(),
      this.loader.loadSuperMap3D(),
      this.loader.loadIClient3D()
    ]);
  }

  /**
   * 创建地图实例
   */
  public createMap(config: MapConfig): IMapInstance {
    this.validateConfig(config);

    const container = this.resolveContainer(config.container);

    if (config.type === MapType.MAP_2D) {
      return this.create2DMap(config as Map2DConfig, container);
    } else if (config.type === MapType.MAP_3D) {
      return this.create3DMap(config as Map3DConfig, container);
    } else {
      // 自动判断模式
      return this.autoDetectMapType(config, container);
    }
  }

  /**
   * 自动判断地图类型
   */
  private autoDetectMapType(config: MapConfig, container: HTMLElement): IMapInstance {
    // 根据配置参数自动判断
    const has3DFeatures = this.detect3DFeatures(config);

    if (has3DFeatures) {
      console.log('Auto-detected 3D features, creating 3D map');
      const config3D: Map3DConfig = { ...config, type: MapType.MAP_3D };
      return this.create3DMap(config3D, container);
    } else {
      console.log('Auto-detected 2D features, creating 2D map');
      const config2D: Map2DConfig = { ...config, type: MapType.MAP_2D };
      return this.create2DMap(config2D, container);
    }
  }

  /**
   * 检测3D特征
   */
  private detect3DFeatures(config: any): boolean {
    // 检查是否有3D相关的配置
    const has3DConfig = !!(
      config.scene3DOnly ||
      config.terrainProvider ||
      config.shadows ||
      config.globe ||
      config.skyBox ||
      config.sceneMode ||
      config.pitch > 0
    );

    // 检查样式是否包含3D图层
    const has3DStyle = config.style && typeof config.style === 'object' &&
      config.style.layers &&
      config.style.layers.some((layer: any) =>
        layer.type === 'fill-extrusion' ||
        layer.type === 'hillshade' ||
        layer.type === 'raster-dem'
      );

    return has3DConfig || has3DStyle;
  }

  /**
   * 创建2D地图（同步版本）
   */
  private create2DMap(config: Map2DConfig, container: HTMLElement): IMapInstance {
    const mapboxgl = (window as any).mapboxgl;
    if (!mapboxgl) {
      throw new MapSDKError('MapboxGL not loaded. Please call preloadDependencies() first.');
    }

    // 检查 iClient 是否已加载
    if (!mapboxgl.supermap) {
      throw new MapSDKError('iClient for MapboxGL not loaded. Please call preloadDependencies() first.');
    }

    // 创建MapboxGL实例（同步）
    const mapConfig = this.prepare2DConfig(config);
    const mapInstance = new mapboxgl.Map(mapConfig);

    // 注意：同步版本不等待地图加载完成，立即返回
    return new MapboxAdapter(mapInstance, container);
  }

  /**
   * 创建3D地图
   */
  private create3DMap(config: Map3DConfig, container: HTMLElement): IMapInstance {
    const SuperMap3D = (window as any).SuperMap3D;
    if (!SuperMap3D) {
      throw new MapSDKError('SuperMap3D failed to load');
    }

    // 创建SuperMap3D实例
    const viewerConfig = this.prepare3DConfig(config, container);
    const viewer = new SuperMap3D.Viewer(container, viewerConfig);

    if (viewer.scene) {
      // 设置初始视角
      if (config.center) {
        const destination = SuperMap3D.Cartesian3.fromDegrees(
          config.center[0],
          config.center[1],
          config.zoom ? this.zoomToHeight(config.zoom) : 10000
        );

        viewer.camera.setView({
          destination,
          orientation: {
            heading: SuperMap3D.Math.toRadians(config.bearing || 0),
            pitch: SuperMap3D.Math.toRadians(config.pitch || -90),
            roll: 0
          }
        });
      }
    };

    return new SuperMap3DAdapter(viewer, container);
  }

  /**
   * 准备2D地图配置
   */
  private prepare2DConfig(config: Map2DConfig): any {
    const { type, container, ...mapboxConfig } = config;

    return {
      container: typeof container === 'string' ? container : container.id || 'map',
      center: config.center || [0, 0],
      zoom: config.zoom || 0,
      bearing: config.bearing || 0,
      pitch: config.pitch || 0,
      ...mapboxConfig
    };
  }

  /**
   * 准备3D场景配置
   */
  private prepare3DConfig(config: Map3DConfig, container: HTMLElement): any {
    const { type, center, zoom, bearing, pitch, ...viewerConfig } = config;

    return {
      animation: config.animation !== false,
      timeline: config.timeline !== false,
      baseLayerPicker: config.baseLayerPicker !== false,
      fullscreenButton: config.fullscreenButton !== false,
      geocoder: config.geocoder !== false,
      homeButton: config.homeButton !== false,
      sceneModePicker: config.sceneModePicker !== false,
      navigationHelpButton: config.navigationHelpButton !== false,
      ...viewerConfig
    };
  }

  /**
   * 将zoom级别转换为3D相机高度
   */
  private zoomToHeight(zoom: number): number {
    return 40075016.686 / Math.pow(2, zoom + 8);
  }

  /**
   * 解析容器
   */
  private resolveContainer(container: string | HTMLElement): HTMLElement {
    if (typeof container === 'string') {
      const element = document.getElementById(container) || document.querySelector(container);
      if (!element) {
        throw new MapSDKError(`Container element not found: ${container}`);
      }
      return element as HTMLElement;
    }
    return container;
  }

  /**
   * 验证配置
   */
  private validateConfig(config: MapConfig): void {
    if (!config) {
      throw new MapSDKError('Map configuration is required');
    }

    if (!config.container) {
      throw new MapSDKError('Container is required');
    }

    if (config.type && !Object.values(MapType).includes(config.type)) {
      throw new MapSDKError(`Invalid map type: ${config.type}`);
    }
  }

  /**
   * 获取加载状态
   */
  public getLoadingState() {
    return this.loader.getLoadingState();
  }
}
