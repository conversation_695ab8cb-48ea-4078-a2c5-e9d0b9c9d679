<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一地图SDK示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }

        .control-group input,
        .control-group select,
        .control-group button {
            padding: 8px 12px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .control-group button {
            background-color: #007cba;
            color: white;
            border: none;
            cursor: pointer;
        }

        .control-group button:hover {
            background-color: #005a87;
        }

        .control-group button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .map-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #map {
            width: 100%;
            height: 600px;
        }

        .status {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .status h3 {
            margin-top: 0;
        }

        .status-item {
            margin-bottom: 8px;
        }

        .status-item strong {
            display: inline-block;
            width: 100px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>统一地图SDK示例</h1>
            <p>整合SuperMap iClient3D和SuperMap iClient for MapboxGL的统一SDK</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>地图类型:</label>
                <select id="mapType">
                    <option value="auto">自动检测</option>
                    <option value="2d">2D地图</option>
                    <option value="3d">3D场景</option>
                </select>
            </div>

            <div class="control-group">
                <label>中心点:</label>
                <input type="number" id="centerLng" placeholder="经度" value="116.3974" step="0.0001">
                <input type="number" id="centerLat" placeholder="纬度" value="39.9093" step="0.0001">
            </div>

            <div class="control-group">
                <label>缩放级别:</label>
                <input type="number" id="zoom" placeholder="缩放级别" value="10" min="0" max="20">
            </div>

            <div class="control-group">
                <label>方位角:</label>
                <input type="number" id="bearing" placeholder="方位角" value="0" min="0" max="360">
            </div>

            <div class="control-group">
                <label>倾斜角:</label>
                <input type="number" id="pitch" placeholder="倾斜角" value="0" min="0" max="60">
            </div>

            <div class="control-group">
                <button id="createMap">创建地图</button>
                <button id="destroyMap" disabled>销毁地图</button>
                <button id="addLayer" disabled>添加图层</button>
                <button id="removeLayer" disabled>移除图层</button>
            </div>
        </div>

        <div id="message"></div>

        <div class="map-container">
            <div id="map">
                <div class="loading">
                    <p>请点击"创建地图"按钮开始</p>
                </div>
            </div>
        </div>

        <div class="status">
            <h3>状态信息</h3>
            <div class="status-item">
                <strong>SDK状态:</strong>
                <span id="sdkStatus">未加载</span>
            </div>
            <div class="status-item">
                <strong>地图类型:</strong>
                <span id="currentMapType">无</span>
            </div>
            <div class="status-item">
                <strong>当前中心:</strong>
                <span id="currentCenter">无</span>
            </div>
            <div class="status-item">
                <strong>当前缩放:</strong>
                <span id="currentZoom">无</span>
            </div>
            <div class="status-item">
                <strong>事件日志:</strong>
                <div id="eventLog" style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; margin-top: 5px; border-radius: 4px;"></div>
            </div>
        </div>
    </div>

    <!-- 加载统一地图SDK -->
    <script src="../dist/sg-map-sdk.umd.js"></script>

    <script>
        // 配置SDK资源路径
        if (typeof window.SGMapSDK !== 'undefined') {
            window.SGMapSDK.setConfig({
                mapboxgl: {
                    js: 'https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.js',
                    css: 'https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.css'
                },
                supermap3d: {
                    js: '../iclient-3d/SuperMap3D/SuperMap3D.js',
                    css: '../iclient-3d/SuperMap3D/Widgets/widgets.css'
                },
                iclientMapboxgl: {
                    js: '../iclient-mapboxgl/iclient-mapboxgl.js',
                    css: '../iclient-mapboxgl/iclient-mapboxgl.css'
                },
                iclient3d: {
                    js: '../iclient-3d/deps.js'
                }
            });
        }

        // 全局变量
        let currentMap = null;
        let hasTestLayer = false;

        // DOM元素
        const elements = {
            mapType: document.getElementById('mapType'),
            centerLng: document.getElementById('centerLng'),
            centerLat: document.getElementById('centerLat'),
            zoom: document.getElementById('zoom'),
            bearing: document.getElementById('bearing'),
            pitch: document.getElementById('pitch'),
            createMap: document.getElementById('createMap'),
            destroyMap: document.getElementById('destroyMap'),
            addLayer: document.getElementById('addLayer'),
            removeLayer: document.getElementById('removeLayer'),
            message: document.getElementById('message'),
            sdkStatus: document.getElementById('sdkStatus'),
            currentMapType: document.getElementById('currentMapType'),
            currentCenter: document.getElementById('currentCenter'),
            currentZoom: document.getElementById('currentZoom'),
            eventLog: document.getElementById('eventLog')
        };

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageEl = elements.message;
            messageEl.className = type;
            messageEl.textContent = message;
            messageEl.style.display = 'block';

            if (type === 'success') {
                setTimeout(() => {
                    messageEl.style.display = 'none';
                }, 3000);
            }
        }

        // 记录事件
        function logEvent(event) {
            const eventLog = elements.eventLog;
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${time}] ${event}`;
            eventLog.appendChild(logEntry);
            eventLog.scrollTop = eventLog.scrollHeight;
        }

        // 更新状态
        function updateStatus() {
            if (currentMap) {
                try {
                    const center = currentMap.getCenter();
                    const zoom = currentMap.getZoom();
                    const mapType = currentMap.mapType || 'unknown';

                    elements.currentMapType.textContent = mapType;
                    elements.currentCenter.textContent = `${center[0].toFixed(4)}, ${center[1].toFixed(4)}`;
                    elements.currentZoom.textContent = zoom.toFixed(2);
                } catch (error) {
                    console.warn('Failed to update status:', error);
                }
            }
        }

        // 创建地图
        async function createMap() {
            try {
                showMessage('正在创建地图...', 'loading');
                elements.createMap.disabled = true;

                // 获取配置
                const config = {
                    container: 'map',
                    center: [
                        parseFloat(elements.centerLng.value),
                        parseFloat(elements.centerLat.value)
                    ],
                    zoom: parseFloat(elements.zoom.value),
                    bearing: parseFloat(elements.bearing.value),
                    pitch: parseFloat(elements.pitch.value)
                };

                // 根据选择的类型添加type
                const mapType = elements.mapType.value;
                if (mapType !== 'auto') {
                    config.type = mapType;
                }

                // 创建地图
                currentMap = await window.createMap(config);

                // 设置事件监听
                setupEventListeners();

                // 更新UI状态
                elements.destroyMap.disabled = false;
                elements.addLayer.disabled = false;
                elements.createMap.disabled = false;

                showMessage('地图创建成功！', 'success');
                logEvent('地图创建成功');
                updateStatus();

            } catch (error) {
                console.error('创建地图失败:', error);
                showMessage(`创建地图失败: ${error.message}`, 'error');
                elements.createMap.disabled = false;
            }
        }

        // 设置事件监听
        function setupEventListeners() {
            if (!currentMap) return;

            // 地图事件
            const events = ['load', 'move', 'zoom', 'click', 'moveend', 'zoomend'];

            events.forEach(eventType => {
                try {
                    currentMap.on(eventType, (e) => {
                        logEvent(`${eventType} 事件触发`);
                        if (eventType === 'moveend' || eventType === 'zoomend') {
                            updateStatus();
                        }
                    });
                } catch (error) {
                    console.warn(`Failed to add ${eventType} listener:`, error);
                }
            });
        }

        // 销毁地图
        function destroyMap() {
            if (currentMap) {
                try {
                    currentMap.remove();
                    currentMap = null;
                    hasTestLayer = false;

                    // 重置UI
                    elements.destroyMap.disabled = true;
                    elements.addLayer.disabled = true;
                    elements.removeLayer.disabled = true;

                    elements.currentMapType.textContent = '无';
                    elements.currentCenter.textContent = '无';
                    elements.currentZoom.textContent = '无';

                    document.getElementById('map').innerHTML = '<div class="loading"><p>请点击"创建地图"按钮开始</p></div>';

                    showMessage('地图已销毁', 'success');
                    logEvent('地图已销毁');
                } catch (error) {
                    console.error('销毁地图失败:', error);
                    showMessage(`销毁地图失败: ${error.message}`, 'error');
                }
            }
        }

        // 添加测试图层
        function addTestLayer() {
            if (!currentMap || hasTestLayer) return;

            try {
                // 添加数据源
                currentMap.addSource('test-source', {
                    type: 'geojson',
                    data: {
                        type: 'Feature',
                        geometry: {
                            type: 'Point',
                            coordinates: [116.3974, 39.9093]
                        },
                        properties: {
                            name: '测试点'
                        }
                    }
                });

                // 添加图层
                currentMap.addLayer({
                    id: 'test-layer',
                    type: 'circle',
                    source: 'test-source',
                    paint: {
                        'circle-radius': 10,
                        'circle-color': '#ff0000'
                    }
                });

                hasTestLayer = true;
                elements.removeLayer.disabled = false;
                showMessage('测试图层添加成功', 'success');
                logEvent('添加测试图层');
            } catch (error) {
                console.error('添加图层失败:', error);
                showMessage(`添加图层失败: ${error.message}`, 'error');
            }
        }

        // 移除测试图层
        function removeTestLayer() {
            if (!currentMap || !hasTestLayer) return;

            try {
                currentMap.removeLayer('test-layer');
                currentMap.removeSource('test-source');

                hasTestLayer = false;
                elements.removeLayer.disabled = true;
                showMessage('测试图层移除成功', 'success');
                logEvent('移除测试图层');
            } catch (error) {
                console.error('移除图层失败:', error);
                showMessage(`移除图层失败: ${error.message}`, 'error');
            }
        }

        // 绑定事件
        elements.createMap.addEventListener('click', createMap);
        elements.destroyMap.addEventListener('click', destroyMap);
        elements.addLayer.addEventListener('click', addTestLayer);
        elements.removeLayer.addEventListener('click', removeTestLayer);

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            logEvent('页面加载完成');

            // 检查SDK状态
            if (typeof window.SGMapSDK !== 'undefined') {
                elements.sdkStatus.textContent = '已加载';
                logEvent('SDK已加载');

                // 检查createMap函数
                if (typeof window.createMap !== 'undefined') {
                    logEvent('createMap函数可用');
                } else {
                    logEvent('createMap函数不可用');
                }
            } else {
                elements.sdkStatus.textContent = '未加载';
                logEvent('SDK未加载');
                showMessage('SDK未正确加载，请检查控制台错误信息', 'error');
            }
        });
    </script>
</body>
</html>
