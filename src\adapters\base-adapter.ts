/**
 * 基础适配器 - 定义统一的地图接口
 */

import { IMapInstance, LayerConfig, SourceConfig, MapEvent, MapSDKError } from '../types';

export abstract class BaseMapAdapter implements IMapInstance {
  protected instance: any;
  protected container: HTMLElement;
  protected eventListeners: Map<string, Set<Function>> = new Map();

  constructor(instance: any, container: HTMLElement) {
    this.instance = instance;
    this.container = container;
    this.setupEventForwarding();
  }

  /**
   * 设置事件转发机制
   */
  protected abstract setupEventForwarding(): void;

  /**
   * 标准化事件对象
   */
  protected normalizeEvent(originalEvent: any, type: string): MapEvent {
    return {
      type,
      target: this,
      originalEvent: originalEvent.originalEvent || originalEvent,
      point: originalEvent.point,
      lngLat: originalEvent.lngLat,
      features: originalEvent.features
    };
  }

  // 基础方法
  public getContainer(): HTMLElement {
    return this.container;
  }

  public abstract getCenter(): [number, number];
  public abstract setCenter(center: [number, number]): this;
  public abstract getZoom(): number;
  public abstract setZoom(zoom: number): this;
  public abstract getBearing(): number;
  public abstract setBearing(bearing: number): this;
  public abstract getPitch(): number;
  public abstract setPitch(pitch: number): this;

  // 图层管理
  public abstract addLayer(layer: LayerConfig): this;
  public abstract removeLayer(layerId: string): this;
  public abstract getLayer(layerId: string): any;
  public abstract moveLayer(layerId: string, beforeId?: string): this;

  // 数据源管理
  public abstract addSource(sourceId: string, source: SourceConfig): this;
  public abstract removeSource(sourceId: string): this;
  public abstract getSource(sourceId: string): any;

  // 事件管理
  public on(type: string, listener: (event: MapEvent) => void): this {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
    return this;
  }

  public off(type: string, listener?: (event: MapEvent) => void): this {
    if (!this.eventListeners.has(type)) {
      return this;
    }

    const listeners = this.eventListeners.get(type)!;
    if (listener) {
      listeners.delete(listener);
    } else {
      listeners.clear();
    }

    if (listeners.size === 0) {
      this.eventListeners.delete(type);
    }

    return this;
  }

  public fire(type: string, data?: any): this {
    if (!this.eventListeners.has(type)) {
      return this;
    }

    const event: MapEvent = {
      type,
      target: this,
      ...data
    };

    this.eventListeners.get(type)!.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error(`Error in event listener for ${type}:`, error);
      }
    });

    return this;
  }

  // 控制方法
  public abstract resize(): this;
  public abstract remove(): void;

  // 获取原始实例
  public getOriginalInstance(): any {
    return this.instance;
  }

  /**
   * 验证图层配置
   */
  protected validateLayerConfig(layer: LayerConfig): void {
    if (!layer.id) {
      throw new MapSDKError('Layer must have an id');
    }
    if (!layer.type) {
      throw new MapSDKError('Layer must have a type');
    }
  }

  /**
   * 验证数据源配置
   */
  protected validateSourceConfig(source: SourceConfig): void {
    if (!source.type) {
      throw new MapSDKError('Source must have a type');
    }
  }

  /**
   * 安全执行方法
   */
  protected safeExecute<T>(fn: () => T, errorMessage: string): T {
    try {
      return fn();
    } catch (error: any) {
      throw new MapSDKError(`${errorMessage}: ${error.message}`);
    }
  }

  /**
   * 检查实例是否有效
   */
  protected checkInstance(): void {
    if (!this.instance) {
      throw new MapSDKError('Map instance is not available');
    }
  }
}
