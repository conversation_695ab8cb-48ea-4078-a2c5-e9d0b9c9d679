(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else if(typeof exports === 'object')
		exports["SGMapSDK"] = factory();
	else
		root["SGMapSDK"] = factory();
})(this, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/adapters/base-adapter.ts":
/*!**************************************!*\
  !*** ./src/adapters/base-adapter.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseMapAdapter: () => (/* binding */ BaseMapAdapter)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * 基础适配器 - 定义统一的地图接口
 */


var BaseMapAdapter = /*#__PURE__*/function () {
  function BaseMapAdapter(instance, container) {
    _classCallCheck(this, BaseMapAdapter);
    _defineProperty(this, "eventListeners", new Map());
    this.instance = instance;
    this.container = container;
    this.setupEventForwarding();
  }

  /**
   * 设置事件转发机制
   */
  return _createClass(BaseMapAdapter, [{
    key: "normalizeEvent",
    value:
    /**
     * 标准化事件对象
     */
    function normalizeEvent(originalEvent, type) {
      return {
        type: type,
        target: this,
        originalEvent: originalEvent.originalEvent || originalEvent,
        point: originalEvent.point,
        lngLat: originalEvent.lngLat,
        features: originalEvent.features
      };
    }

    // 基础方法
  }, {
    key: "getContainer",
    value: function getContainer() {
      return this.container;
    }
  }, {
    key: "on",
    value:
    // 事件管理
    function on(type, listener) {
      if (!this.eventListeners.has(type)) {
        this.eventListeners.set(type, new Set());
      }
      this.eventListeners.get(type).add(listener);
      return this;
    }
  }, {
    key: "off",
    value: function off(type, listener) {
      if (!this.eventListeners.has(type)) {
        return this;
      }
      var listeners = this.eventListeners.get(type);
      if (listener) {
        listeners.delete(listener);
      } else {
        listeners.clear();
      }
      if (listeners.size === 0) {
        this.eventListeners.delete(type);
      }
      return this;
    }
  }, {
    key: "fire",
    value: function fire(type, data) {
      if (!this.eventListeners.has(type)) {
        return this;
      }
      var event = _objectSpread({
        type: type,
        target: this
      }, data);
      this.eventListeners.get(type).forEach(function (listener) {
        try {
          listener(event);
        } catch (error) {
          console.error("Error in event listener for ".concat(type, ":"), error);
        }
      });
      return this;
    }

    // 控制方法
  }, {
    key: "getOriginalInstance",
    value:
    // 获取原始实例
    function getOriginalInstance() {
      return this.instance;
    }

    /**
     * 验证图层配置
     */
  }, {
    key: "validateLayerConfig",
    value: function validateLayerConfig(layer) {
      if (!layer.id) {
        throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('Layer must have an id');
      }
      if (!layer.type) {
        throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('Layer must have a type');
      }
    }

    /**
     * 验证数据源配置
     */
  }, {
    key: "validateSourceConfig",
    value: function validateSourceConfig(source) {
      if (!source.type) {
        throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('Source must have a type');
      }
    }

    /**
     * 安全执行方法
     */
  }, {
    key: "safeExecute",
    value: function safeExecute(fn, errorMessage) {
      try {
        return fn();
      } catch (error) {
        throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError("".concat(errorMessage, ": ").concat(error.message));
      }
    }

    /**
     * 检查实例是否有效
     */
  }, {
    key: "checkInstance",
    value: function checkInstance() {
      if (!this.instance) {
        throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('Map instance is not available');
      }
    }
  }]);
}();

/***/ }),

/***/ "./src/adapters/mapbox-adapter.ts":
/*!****************************************!*\
  !*** ./src/adapters/mapbox-adapter.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MapboxAdapter: () => (/* binding */ MapboxAdapter)
/* harmony export */ });
/* harmony import */ var _base_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base-adapter */ "./src/adapters/base-adapter.ts");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
/**
 * MapboxGL适配器 - 适配MapboxGL地图
 */


var MapboxAdapter = /*#__PURE__*/function (_BaseMapAdapter) {
  function MapboxAdapter() {
    _classCallCheck(this, MapboxAdapter);
    return _callSuper(this, MapboxAdapter, arguments);
  }
  _inherits(MapboxAdapter, _BaseMapAdapter);
  return _createClass(MapboxAdapter, [{
    key: "setupEventForwarding",
    value:
    /**
     * 设置事件转发机制
     */
    function setupEventForwarding() {
      var _this = this;
      if (!this.instance) return;

      // 常用的地图事件
      var events = ['load', 'idle', 'remove', 'render', 'resize', 'webglcontextlost', 'webglcontextrestored', 'dataloading', 'data', 'tiledataloading', 'sourcedataloading', 'styledataloading', 'sourcedata', 'styledata', 'boxzoomcancel', 'boxzoomstart', 'boxzoomend', 'touchcancel', 'touchmove', 'touchend', 'touchstart', 'click', 'contextmenu', 'dblclick', 'mousemove', 'mouseup', 'mousedown', 'mouseout', 'mouseover', 'movestart', 'move', 'moveend', 'zoomstart', 'zoom', 'zoomend', 'rotatestart', 'rotate', 'rotateend', 'dragstart', 'drag', 'dragend', 'pitchstart', 'pitch', 'pitchend', 'wheel'];
      events.forEach(function (eventType) {
        _this.instance.on(eventType, function (e) {
          var normalizedEvent = _this.normalizeEvent(e, eventType);
          _this.fire(eventType, normalizedEvent);
        });
      });
    }

    // 基础方法实现
  }, {
    key: "getCenter",
    value: function getCenter() {
      this.checkInstance();
      var center = this.instance.getCenter();
      return [center.lng, center.lat];
    }
  }, {
    key: "setCenter",
    value: function setCenter(center) {
      var _this2 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this2.instance.setCenter(center);
      }, 'Failed to set center');
      return this;
    }
  }, {
    key: "getZoom",
    value: function getZoom() {
      this.checkInstance();
      return this.instance.getZoom();
    }
  }, {
    key: "setZoom",
    value: function setZoom(zoom) {
      var _this3 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this3.instance.setZoom(zoom);
      }, 'Failed to set zoom');
      return this;
    }
  }, {
    key: "getBearing",
    value: function getBearing() {
      this.checkInstance();
      return this.instance.getBearing();
    }
  }, {
    key: "setBearing",
    value: function setBearing(bearing) {
      var _this4 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this4.instance.setBearing(bearing);
      }, 'Failed to set bearing');
      return this;
    }
  }, {
    key: "getPitch",
    value: function getPitch() {
      this.checkInstance();
      return this.instance.getPitch();
    }
  }, {
    key: "setPitch",
    value: function setPitch(pitch) {
      var _this5 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this5.instance.setPitch(pitch);
      }, 'Failed to set pitch');
      return this;
    }

    // 图层管理
  }, {
    key: "addLayer",
    value: function addLayer(layer) {
      var _this6 = this;
      this.checkInstance();
      this.validateLayerConfig(layer);
      this.safeExecute(function () {
        _this6.instance.addLayer(layer);
      }, "Failed to add layer ".concat(layer.id));
      return this;
    }
  }, {
    key: "removeLayer",
    value: function removeLayer(layerId) {
      var _this7 = this;
      this.checkInstance();
      this.safeExecute(function () {
        if (_this7.instance.getLayer(layerId)) {
          _this7.instance.removeLayer(layerId);
        }
      }, "Failed to remove layer ".concat(layerId));
      return this;
    }
  }, {
    key: "getLayer",
    value: function getLayer(layerId) {
      this.checkInstance();
      return this.instance.getLayer(layerId);
    }
  }, {
    key: "moveLayer",
    value: function moveLayer(layerId, beforeId) {
      var _this8 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this8.instance.moveLayer(layerId, beforeId);
      }, "Failed to move layer ".concat(layerId));
      return this;
    }

    // 数据源管理
  }, {
    key: "addSource",
    value: function addSource(sourceId, source) {
      var _this9 = this;
      this.checkInstance();
      this.validateSourceConfig(source);
      this.safeExecute(function () {
        _this9.instance.addSource(sourceId, source);
      }, "Failed to add source ".concat(sourceId));
      return this;
    }
  }, {
    key: "removeSource",
    value: function removeSource(sourceId) {
      var _this0 = this;
      this.checkInstance();
      this.safeExecute(function () {
        if (_this0.instance.getSource(sourceId)) {
          _this0.instance.removeSource(sourceId);
        }
      }, "Failed to remove source ".concat(sourceId));
      return this;
    }
  }, {
    key: "getSource",
    value: function getSource(sourceId) {
      this.checkInstance();
      return this.instance.getSource(sourceId);
    }

    // 控制方法
  }, {
    key: "resize",
    value: function resize() {
      var _this1 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this1.instance.resize();
      }, 'Failed to resize map');
      return this;
    }
  }, {
    key: "remove",
    value: function remove() {
      var _this10 = this;
      if (this.instance) {
        this.safeExecute(function () {
          _this10.instance.remove();
        }, 'Failed to remove map');
        this.instance = null;
      }
      this.eventListeners.clear();
    }

    /**
     * MapboxGL特有方法
     */

    /**
     * 设置样式
     */
  }, {
    key: "setStyle",
    value: function setStyle(style) {
      var _this11 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this11.instance.setStyle(style);
      }, 'Failed to set style');
      return this;
    }

    /**
     * 获取样式
     */
  }, {
    key: "getStyle",
    value: function getStyle() {
      this.checkInstance();
      return this.instance.getStyle();
    }

    /**
     * 飞行到指定位置
     */
  }, {
    key: "flyTo",
    value: function flyTo(options) {
      var _this12 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this12.instance.flyTo(options);
      }, 'Failed to fly to location');
      return this;
    }

    /**
     * 跳转到指定位置
     */
  }, {
    key: "jumpTo",
    value: function jumpTo(options) {
      var _this13 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this13.instance.jumpTo(options);
      }, 'Failed to jump to location');
      return this;
    }

    /**
     * 适配边界
     */
  }, {
    key: "fitBounds",
    value: function fitBounds(bounds, options) {
      var _this14 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this14.instance.fitBounds(bounds, options);
      }, 'Failed to fit bounds');
      return this;
    }

    /**
     * 查询渲染的要素
     */
  }, {
    key: "queryRenderedFeatures",
    value: function queryRenderedFeatures(pointOrBox, options) {
      var _this15 = this;
      this.checkInstance();
      return this.safeExecute(function () {
        return _this15.instance.queryRenderedFeatures(pointOrBox, options);
      }, 'Failed to query rendered features');
    }

    /**
     * 查询源数据要素
     */
  }, {
    key: "querySourceFeatures",
    value: function querySourceFeatures(sourceId, options) {
      var _this16 = this;
      this.checkInstance();
      return this.safeExecute(function () {
        return _this16.instance.querySourceFeatures(sourceId, options);
      }, 'Failed to query source features');
    }

    /**
     * 屏幕坐标转地理坐标
     */
  }, {
    key: "unproject",
    value: function unproject(point) {
      this.checkInstance();
      var lngLat = this.instance.unproject(point);
      return [lngLat.lng, lngLat.lat];
    }

    /**
     * 地理坐标转屏幕坐标
     */
  }, {
    key: "project",
    value: function project(lngLat) {
      this.checkInstance();
      var point = this.instance.project(lngLat);
      return [point.x, point.y];
    }
  }]);
}(_base_adapter__WEBPACK_IMPORTED_MODULE_0__.BaseMapAdapter);

/***/ }),

/***/ "./src/adapters/supermap3d-adapter.ts":
/*!********************************************!*\
  !*** ./src/adapters/supermap3d-adapter.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SuperMap3DAdapter: () => (/* binding */ SuperMap3DAdapter)
/* harmony export */ });
/* harmony import */ var _base_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base-adapter */ "./src/adapters/base-adapter.ts");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
/**
 * SuperMap3D适配器 - 适配SuperMap3D场景
 */


var SuperMap3DAdapter = /*#__PURE__*/function (_BaseMapAdapter) {
  function SuperMap3DAdapter() {
    _classCallCheck(this, SuperMap3DAdapter);
    return _callSuper(this, SuperMap3DAdapter, arguments);
  }
  _inherits(SuperMap3DAdapter, _BaseMapAdapter);
  return _createClass(SuperMap3DAdapter, [{
    key: "setupEventForwarding",
    value:
    /**
     * 设置事件转发机制
     */
    function setupEventForwarding() {
      var _this = this;
      if (!this.instance) return;

      // SuperMap3D的事件处理
      var scene = this.instance.scene;
      var camera = this.instance.camera;
      if (scene && scene.canvas) {
        // 创建事件处理器
        var handler = new window.SuperMap3D.ScreenSpaceEventHandler(scene.canvas);

        // 鼠标点击事件
        handler.setInputAction(function (event) {
          var pickedPosition = scene.pickPosition(event.position);
          var cartographic = window.SuperMap3D.Cartographic.fromCartesian(pickedPosition);
          if (cartographic) {
            var longitude = window.SuperMap3D.Math.toDegrees(cartographic.longitude);
            var latitude = window.SuperMap3D.Math.toDegrees(cartographic.latitude);
            var normalizedEvent = _this.normalizeEvent({
              originalEvent: event,
              point: [event.position.x, event.position.y],
              lngLat: [longitude, latitude]
            }, 'click');
            _this.fire('click', normalizedEvent);
          }
        }, window.SuperMap3D.ScreenSpaceEventType.LEFT_CLICK);

        // 鼠标移动事件
        handler.setInputAction(function (event) {
          var normalizedEvent = _this.normalizeEvent({
            originalEvent: event,
            point: [event.endPosition.x, event.endPosition.y]
          }, 'mousemove');
          _this.fire('mousemove', normalizedEvent);
        }, window.SuperMap3D.ScreenSpaceEventType.MOUSE_MOVE);

        // 相机移动事件
        if (camera) {
          camera.moveStart.addEventListener(function () {
            _this.fire('movestart', {
              type: 'movestart',
              target: _this
            });
          });
          camera.moveEnd.addEventListener(function () {
            _this.fire('moveend', {
              type: 'moveend',
              target: _this
            });
          });
        }

        // 场景渲染事件
        if (scene.postRender) {
          scene.postRender.addEventListener(function () {
            _this.fire('render', {
              type: 'render',
              target: _this
            });
          });
        }
      }
    }

    // 基础方法实现
  }, {
    key: "getCenter",
    value: function getCenter() {
      this.checkInstance();
      var camera = this.instance.camera;
      var cartographic = window.SuperMap3D.Cartographic.fromCartesian(camera.position);
      var longitude = window.SuperMap3D.Math.toDegrees(cartographic.longitude);
      var latitude = window.SuperMap3D.Math.toDegrees(cartographic.latitude);
      return [longitude, latitude];
    }
  }, {
    key: "setCenter",
    value: function setCenter(center) {
      var _this2 = this;
      this.checkInstance();
      this.safeExecute(function () {
        var destination = window.SuperMap3D.Cartesian3.fromDegrees(center[0], center[1], 1000);
        _this2.instance.camera.setView({
          destination: destination
        });
      }, 'Failed to set center');
      return this;
    }
  }, {
    key: "getZoom",
    value: function getZoom() {
      this.checkInstance();
      // SuperMap3D没有直接的zoom概念，这里通过相机高度模拟
      var camera = this.instance.camera;
      var cartographic = window.SuperMap3D.Cartographic.fromCartesian(camera.position);
      var height = cartographic.height;
      // 将高度转换为类似zoom级别的值
      return Math.log2(40075016.686 / height) - 8;
    }
  }, {
    key: "setZoom",
    value: function setZoom(zoom) {
      var _this3 = this;
      this.checkInstance();
      this.safeExecute(function () {
        // 将zoom级别转换为相机高度
        var height = 40075016.686 / Math.pow(2, zoom + 8);
        var camera = _this3.instance.camera;
        var cartographic = window.SuperMap3D.Cartographic.fromCartesian(camera.position);
        var longitude = window.SuperMap3D.Math.toDegrees(cartographic.longitude);
        var latitude = window.SuperMap3D.Math.toDegrees(cartographic.latitude);
        var destination = window.SuperMap3D.Cartesian3.fromDegrees(longitude, latitude, height);
        camera.setView({
          destination: destination
        });
      }, 'Failed to set zoom');
      return this;
    }
  }, {
    key: "getBearing",
    value: function getBearing() {
      this.checkInstance();
      var camera = this.instance.camera;
      return window.SuperMap3D.Math.toDegrees(camera.heading);
    }
  }, {
    key: "setBearing",
    value: function setBearing(bearing) {
      var _this4 = this;
      this.checkInstance();
      this.safeExecute(function () {
        var camera = _this4.instance.camera;
        var heading = window.SuperMap3D.Math.toRadians(bearing);
        camera.setView({
          orientation: {
            heading: heading,
            pitch: camera.pitch,
            roll: camera.roll
          }
        });
      }, 'Failed to set bearing');
      return this;
    }
  }, {
    key: "getPitch",
    value: function getPitch() {
      this.checkInstance();
      var camera = this.instance.camera;
      return window.SuperMap3D.Math.toDegrees(camera.pitch);
    }
  }, {
    key: "setPitch",
    value: function setPitch(pitch) {
      var _this5 = this;
      this.checkInstance();
      this.safeExecute(function () {
        var camera = _this5.instance.camera;
        var pitchRadians = window.SuperMap3D.Math.toRadians(pitch);
        camera.setView({
          orientation: {
            heading: camera.heading,
            pitch: pitchRadians,
            roll: camera.roll
          }
        });
      }, 'Failed to set pitch');
      return this;
    }

    // 图层管理（SuperMap3D的图层概念不同，这里做适配）
  }, {
    key: "addLayer",
    value: function addLayer(layer) {
      var _this6 = this;
      this.checkInstance();
      this.validateLayerConfig(layer);
      this.safeExecute(function () {
        var scene = _this6.instance.scene;

        // 根据图层类型进行不同处理
        switch (layer.type) {
          case 'imagery':
            _this6.addImageryLayer(layer);
            break;
          case 's3m':
            _this6.addS3MLayer(layer);
            break;
          case 'terrain':
            _this6.addTerrainLayer(layer);
            break;
          case 'mvt':
            _this6.addMVTLayer(layer);
            break;
          default:
            console.warn("Layer type ".concat(layer.type, " is not supported in 3D mode"));
        }
      }, "Failed to add layer ".concat(layer.id));
      return this;
    }
  }, {
    key: "addImageryLayer",
    value: function addImageryLayer(layer) {
      var scene = this.instance.scene;
      var imageryLayers = this.instance.imageryLayers;
      if (layer.source && _typeof(layer.source) === 'object' && layer.source.url) {
        var provider = new window.SuperMap3D.SuperMapImageryProvider({
          url: layer.source.url
        });
        imageryLayers.addImageryProvider(provider);
      }
    }
  }, {
    key: "addS3MLayer",
    value: function addS3MLayer(layer) {
      var scene = this.instance.scene;
      if (layer.source && _typeof(layer.source) === 'object' && layer.source.url) {
        scene.addS3MTilesLayerByScp(layer.source.url, {
          name: layer.id
        });
      }
    }
  }, {
    key: "addTerrainLayer",
    value: function addTerrainLayer(layer) {
      if (layer.source && _typeof(layer.source) === 'object' && layer.source.url) {
        var terrainProvider = new window.SuperMap3D.SuperMapTerrainProvider({
          url: layer.source.url,
          isSct: true
        });
        this.instance.terrainProvider = terrainProvider;
      }
    }
  }, {
    key: "addMVTLayer",
    value: function addMVTLayer(layer) {
      var scene = this.instance.scene;
      if (layer.source && _typeof(layer.source) === 'object' && layer.source.url) {
        scene.addVectorTilesMap({
          url: layer.source.url,
          name: layer.id,
          viewer: this.instance
        });
      }
    }
  }, {
    key: "removeLayer",
    value: function removeLayer(layerId) {
      var _this7 = this;
      this.checkInstance();
      this.safeExecute(function () {
        // SuperMap3D的图层移除需要根据类型进行不同处理
        var scene = _this7.instance.scene;
        var imageryLayers = _this7.instance.imageryLayers;

        // 尝试从影像图层中移除
        for (var i = 0; i < imageryLayers.length; i++) {
          var layer = imageryLayers.get(i);
          if (layer.name === layerId) {
            imageryLayers.remove(layer);
            break;
          }
        }

        // 尝试从S3M图层中移除
        if (scene.layers) {
          scene.layers.removeAll();
        }
      }, "Failed to remove layer ".concat(layerId));
      return this;
    }
  }, {
    key: "getLayer",
    value: function getLayer(layerId) {
      this.checkInstance();
      // SuperMap3D的图层获取比较复杂，这里简化处理
      return null;
    }
  }, {
    key: "moveLayer",
    value: function moveLayer(layerId, beforeId) {
      this.checkInstance();
      console.warn('moveLayer is not fully supported in 3D mode');
      return this;
    }

    // 数据源管理
  }, {
    key: "addSource",
    value: function addSource(sourceId, source) {
      this.checkInstance();
      this.validateSourceConfig(source);

      // SuperMap3D的数据源概念不同，这里做简单适配
      console.warn('addSource in 3D mode is handled through layer configuration');
      return this;
    }
  }, {
    key: "removeSource",
    value: function removeSource(sourceId) {
      this.checkInstance();
      console.warn('removeSource in 3D mode is handled through layer management');
      return this;
    }
  }, {
    key: "getSource",
    value: function getSource(sourceId) {
      this.checkInstance();
      return null;
    }

    // 控制方法
  }, {
    key: "resize",
    value: function resize() {
      var _this8 = this;
      this.checkInstance();
      this.safeExecute(function () {
        _this8.instance.resize();
      }, 'Failed to resize scene');
      return this;
    }
  }, {
    key: "remove",
    value: function remove() {
      var _this9 = this;
      if (this.instance) {
        this.safeExecute(function () {
          _this9.instance.destroy();
        }, 'Failed to destroy scene');
        this.instance = null;
      }
      this.eventListeners.clear();
    }

    /**
     * SuperMap3D特有方法
     */

    /**
     * 飞行到指定位置
     */
  }, {
    key: "flyTo",
    value: function flyTo(options) {
      var _this0 = this;
      this.checkInstance();
      this.safeExecute(function () {
        var camera = _this0.instance.camera;
        camera.flyTo(options);
      }, 'Failed to fly to location');
      return this;
    }

    /**
     * 设置相机视角
     */
  }, {
    key: "setView",
    value: function setView(options) {
      var _this1 = this;
      this.checkInstance();
      this.safeExecute(function () {
        var camera = _this1.instance.camera;
        camera.setView(options);
      }, 'Failed to set view');
      return this;
    }

    /**
     * 获取场景对象
     */
  }, {
    key: "getScene",
    value: function getScene() {
      this.checkInstance();
      return this.instance.scene;
    }

    /**
     * 获取相机对象
     */
  }, {
    key: "getCamera",
    value: function getCamera() {
      this.checkInstance();
      return this.instance.camera;
    }
  }]);
}(_base_adapter__WEBPACK_IMPORTED_MODULE_0__.BaseMapAdapter);

/***/ }),

/***/ "./src/core/map-factory.ts":
/*!*********************************!*\
  !*** ./src/core/map-factory.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MapFactory: () => (/* binding */ MapFactory)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
/* harmony import */ var _utils_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/loader */ "./src/utils/loader.ts");
/* harmony import */ var _adapters_mapbox_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../adapters/mapbox-adapter */ "./src/adapters/mapbox-adapter.ts");
/* harmony import */ var _adapters_supermap3d_adapter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../adapters/supermap3d-adapter */ "./src/adapters/supermap3d-adapter.ts");
var _excluded = ["type", "container"],
  _excluded2 = ["type", "center", "zoom", "bearing", "pitch"];
function _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }
function _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * 地图工厂 - 根据配置创建相应的地图实例
 */





var MapFactory = /*#__PURE__*/function () {
  function MapFactory() {
    _classCallCheck(this, MapFactory);
    _defineProperty(this, "defaultConfig", {
      mapboxgl: {
        js: 'https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.js',
        css: 'https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.css'
      },
      supermap3d: {
        js: './iclient-3d/SuperMap3D/SuperMap3D.js',
        css: './iclient-3d/SuperMap3D/Widgets/widgets.css'
      },
      iclientMapboxgl: {
        js: './iclient-mapboxgl/iclient-mapboxgl.js',
        css: './iclient-mapboxgl/iclient-mapboxgl.css'
      },
      iclient3d: {
        js: './iclient-3d/deps.js'
      }
    });
    this.loader = _utils_loader__WEBPACK_IMPORTED_MODULE_1__.ResourceLoader.getInstance();
    this.loader.setConfig(this.defaultConfig);
  }
  return _createClass(MapFactory, [{
    key: "setConfig",
    value:
    /**
     * 设置SDK资源配置
     */
    function setConfig(config) {
      this.defaultConfig = _objectSpread(_objectSpread({}, this.defaultConfig), config);
      this.loader.setConfig(this.defaultConfig);
    }

    /**
     * 创建地图实例
     */
  }, {
    key: "createMap",
    value: (function () {
      var _createMap = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(config) {
        var container;
        return _regenerator().w(function (_context) {
          while (1) switch (_context.n) {
            case 0:
              this.validateConfig(config);
              container = this.resolveContainer(config.container);
              if (!(config.type === _types__WEBPACK_IMPORTED_MODULE_0__.MapType.MAP_2D)) {
                _context.n = 1;
                break;
              }
              return _context.a(2, this.create2DMap(config, container));
            case 1:
              if (!(config.type === _types__WEBPACK_IMPORTED_MODULE_0__.MapType.MAP_3D)) {
                _context.n = 2;
                break;
              }
              return _context.a(2, this.create3DMap(config, container));
            case 2:
              return _context.a(2, this.autoDetectMapType(config, container));
            case 3:
              return _context.a(2);
          }
        }, _callee, this);
      }));
      function createMap(_x) {
        return _createMap.apply(this, arguments);
      }
      return createMap;
    }()
    /**
     * 自动判断地图类型
     */
    )
  }, {
    key: "autoDetectMapType",
    value: (function () {
      var _autoDetectMapType = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(config, container) {
        var has3DFeatures, config3D, config2D;
        return _regenerator().w(function (_context2) {
          while (1) switch (_context2.n) {
            case 0:
              // 根据配置参数自动判断
              has3DFeatures = this.detect3DFeatures(config);
              if (!has3DFeatures) {
                _context2.n = 1;
                break;
              }
              console.log('Auto-detected 3D features, creating 3D map');
              config3D = _objectSpread(_objectSpread({}, config), {}, {
                type: _types__WEBPACK_IMPORTED_MODULE_0__.MapType.MAP_3D
              });
              return _context2.a(2, this.create3DMap(config3D, container));
            case 1:
              console.log('Auto-detected 2D features, creating 2D map');
              config2D = _objectSpread(_objectSpread({}, config), {}, {
                type: _types__WEBPACK_IMPORTED_MODULE_0__.MapType.MAP_2D
              });
              return _context2.a(2, this.create2DMap(config2D, container));
            case 2:
              return _context2.a(2);
          }
        }, _callee2, this);
      }));
      function autoDetectMapType(_x2, _x3) {
        return _autoDetectMapType.apply(this, arguments);
      }
      return autoDetectMapType;
    }()
    /**
     * 检测3D特征
     */
    )
  }, {
    key: "detect3DFeatures",
    value: function detect3DFeatures(config) {
      // 检查是否有3D相关的配置
      var has3DConfig = !!(config.scene3DOnly || config.terrainProvider || config.shadows || config.globe || config.skyBox || config.sceneMode || config.pitch > 0);

      // 检查样式是否包含3D图层
      var has3DStyle = config.style && _typeof(config.style) === 'object' && config.style.layers && config.style.layers.some(function (layer) {
        return layer.type === 'fill-extrusion' || layer.type === 'hillshade' || layer.type === 'raster-dem';
      });
      return has3DConfig || has3DStyle;
    }

    /**
     * 创建2D地图
     */
  }, {
    key: "create2DMap",
    value: (function () {
      var _create2DMap = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(config, container) {
        var mapboxgl, mapConfig, mapInstance;
        return _regenerator().w(function (_context3) {
          while (1) switch (_context3.n) {
            case 0:
              _context3.n = 1;
              return Promise.all([this.loader.loadMapboxGL(), this.loader.loadIClientMapboxGL()]);
            case 1:
              mapboxgl = window.mapboxgl;
              if (mapboxgl) {
                _context3.n = 2;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('MapboxGL failed to load');
            case 2:
              // 创建MapboxGL实例
              mapConfig = this.prepare2DConfig(config);
              mapInstance = new mapboxgl.Map(mapConfig); // 等待地图加载完成
              _context3.n = 3;
              return new Promise(function (resolve, reject) {
                mapInstance.on('load', function () {
                  return resolve();
                });
                mapInstance.on('error', function (e) {
                  var _e$error;
                  return reject(new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError("Map load error: ".concat(((_e$error = e.error) === null || _e$error === void 0 ? void 0 : _e$error.message) || 'Unknown error')));
                });

                // 设置超时
                setTimeout(function () {
                  return reject(new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('Map load timeout'));
                }, 30000);
              });
            case 3:
              return _context3.a(2, new _adapters_mapbox_adapter__WEBPACK_IMPORTED_MODULE_2__.MapboxAdapter(mapInstance, container));
          }
        }, _callee3, this);
      }));
      function create2DMap(_x4, _x5) {
        return _create2DMap.apply(this, arguments);
      }
      return create2DMap;
    }()
    /**
     * 创建3D地图
     */
    )
  }, {
    key: "create3DMap",
    value: (function () {
      var _create3DMap = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(config, container) {
        var _this = this;
        var SuperMap3D, viewerConfig, viewer;
        return _regenerator().w(function (_context4) {
          while (1) switch (_context4.n) {
            case 0:
              _context4.n = 1;
              return Promise.all([this.loader.loadSuperMap3D(), this.loader.loadIClient3D()]);
            case 1:
              SuperMap3D = window.SuperMap3D;
              if (SuperMap3D) {
                _context4.n = 2;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('SuperMap3D failed to load');
            case 2:
              // 创建SuperMap3D实例
              viewerConfig = this.prepare3DConfig(config, container);
              viewer = new SuperMap3D.Viewer(container, viewerConfig); // 等待场景准备完成
              _context4.n = 3;
              return new Promise(function (resolve, reject) {
                if (viewer.scene) {
                  // 设置初始视角
                  if (config.center) {
                    var destination = SuperMap3D.Cartesian3.fromDegrees(config.center[0], config.center[1], config.zoom ? _this.zoomToHeight(config.zoom) : 10000);
                    viewer.camera.setView({
                      destination: destination,
                      orientation: {
                        heading: SuperMap3D.Math.toRadians(config.bearing || 0),
                        pitch: SuperMap3D.Math.toRadians(config.pitch || -90),
                        roll: 0
                      }
                    });
                  }
                  resolve();
                } else {
                  reject(new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('Failed to initialize 3D scene'));
                }
              });
            case 3:
              return _context4.a(2, new _adapters_supermap3d_adapter__WEBPACK_IMPORTED_MODULE_3__.SuperMap3DAdapter(viewer, container));
          }
        }, _callee4, this);
      }));
      function create3DMap(_x6, _x7) {
        return _create3DMap.apply(this, arguments);
      }
      return create3DMap;
    }()
    /**
     * 准备2D地图配置
     */
    )
  }, {
    key: "prepare2DConfig",
    value: function prepare2DConfig(config) {
      var type = config.type,
        container = config.container,
        mapboxConfig = _objectWithoutProperties(config, _excluded);
      return _objectSpread({
        container: typeof container === 'string' ? container : container.id || 'map',
        center: config.center || [0, 0],
        zoom: config.zoom || 0,
        bearing: config.bearing || 0,
        pitch: config.pitch || 0
      }, mapboxConfig);
    }

    /**
     * 准备3D场景配置
     */
  }, {
    key: "prepare3DConfig",
    value: function prepare3DConfig(config, container) {
      var type = config.type,
        center = config.center,
        zoom = config.zoom,
        bearing = config.bearing,
        pitch = config.pitch,
        viewerConfig = _objectWithoutProperties(config, _excluded2);
      return _objectSpread({
        animation: config.animation !== false,
        timeline: config.timeline !== false,
        baseLayerPicker: config.baseLayerPicker !== false,
        fullscreenButton: config.fullscreenButton !== false,
        geocoder: config.geocoder !== false,
        homeButton: config.homeButton !== false,
        sceneModePicker: config.sceneModePicker !== false,
        navigationHelpButton: config.navigationHelpButton !== false
      }, viewerConfig);
    }

    /**
     * 将zoom级别转换为3D相机高度
     */
  }, {
    key: "zoomToHeight",
    value: function zoomToHeight(zoom) {
      return 40075016.686 / Math.pow(2, zoom + 8);
    }

    /**
     * 解析容器
     */
  }, {
    key: "resolveContainer",
    value: function resolveContainer(container) {
      if (typeof container === 'string') {
        var element = document.getElementById(container) || document.querySelector(container);
        if (!element) {
          throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError("Container element not found: ".concat(container));
        }
        return element;
      }
      return container;
    }

    /**
     * 验证配置
     */
  }, {
    key: "validateConfig",
    value: function validateConfig(config) {
      if (!config) {
        throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('Map configuration is required');
      }
      if (!config.container) {
        throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('Container is required');
      }
      if (config.type && !Object.values(_types__WEBPACK_IMPORTED_MODULE_0__.MapType).includes(config.type)) {
        throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError("Invalid map type: ".concat(config.type));
      }
    }

    /**
     * 获取加载状态
     */
  }, {
    key: "getLoadingState",
    value: function getLoadingState() {
      return this.loader.getLoadingState();
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      if (!MapFactory.instance) {
        MapFactory.instance = new MapFactory();
      }
      return MapFactory.instance;
    }
  }]);
}();

/***/ }),

/***/ "./src/core/map-proxy.ts":
/*!*******************************!*\
  !*** ./src/core/map-proxy.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MapProxy: () => (/* binding */ MapProxy)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * 地图代理 - 通过Proxy机制统一不同SDK的接口访问
 */


var MapProxy = /*#__PURE__*/function () {
  function MapProxy(mapInstance) {
    _classCallCheck(this, MapProxy);
    this.mapInstance = mapInstance;
    this.proxy = this.createProxy();
  }

  /**
   * 创建代理对象
   */
  return _createClass(MapProxy, [{
    key: "createProxy",
    value: function createProxy() {
      var _this = this;
      return new Proxy(this.mapInstance, {
        get: function get(target, property, receiver) {
          // 首先检查统一接口中是否存在该属性
          if (property in target) {
            var value = Reflect.get(target, property, receiver);

            // 如果是方法，绑定正确的this上下文
            if (typeof value === 'function') {
              return value.bind(target);
            }
            return value;
          }

          // 检查原始实例中是否存在该属性
          var originalInstance = target.getOriginalInstance();
          if (originalInstance && property in originalInstance) {
            var _value = originalInstance[property];

            // 如果是方法，绑定原始实例的this上下文
            if (typeof _value === 'function') {
              return _value.bind(originalInstance);
            }
            return _value;
          }

          // 特殊处理一些常用的属性和方法
          return _this.handleSpecialProperties(target, property);
        },
        set: function set(target, property, value, receiver) {
          // 首先尝试设置统一接口的属性
          if (property in target) {
            return Reflect.set(target, property, value, receiver);
          }

          // 尝试设置原始实例的属性
          var originalInstance = target.getOriginalInstance();
          if (originalInstance && property in originalInstance) {
            originalInstance[property] = value;
            return true;
          }

          // 如果都不存在，抛出错误
          throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError("Property '".concat(String(property), "' does not exist on map instance"));
        },
        has: function has(target, property) {
          // 检查统一接口
          if (property in target) {
            return true;
          }

          // 检查原始实例
          var originalInstance = target.getOriginalInstance();
          if (originalInstance && property in originalInstance) {
            return true;
          }
          return false;
        },
        ownKeys: function ownKeys(target) {
          var keys = new Set();

          // 添加统一接口的键
          Object.getOwnPropertyNames(target).forEach(function (key) {
            return keys.add(key);
          });
          Object.getOwnPropertySymbols(target).forEach(function (key) {
            return keys.add(key);
          });

          // 添加原始实例的键
          var originalInstance = target.getOriginalInstance();
          if (originalInstance) {
            Object.getOwnPropertyNames(originalInstance).forEach(function (key) {
              return keys.add(key);
            });
            Object.getOwnPropertySymbols(originalInstance).forEach(function (key) {
              return keys.add(key);
            });
          }
          return Array.from(keys);
        },
        getOwnPropertyDescriptor: function getOwnPropertyDescriptor(target, property) {
          // 首先检查统一接口
          var descriptor = Object.getOwnPropertyDescriptor(target, property);
          if (descriptor) {
            return descriptor;
          }

          // 检查原始实例
          var originalInstance = target.getOriginalInstance();
          if (originalInstance) {
            descriptor = Object.getOwnPropertyDescriptor(originalInstance, property);
            if (descriptor) {
              return descriptor;
            }
          }
          return undefined;
        }
      });
    }

    /**
     * 处理特殊属性和方法
     */
  }, {
    key: "handleSpecialProperties",
    value: function handleSpecialProperties(target, property) {
      var originalInstance = target.getOriginalInstance();
      switch (property) {
        // 常用的地图属性别名
        case 'map':
        case 'viewer':
        case 'scene':
          return originalInstance;

        // 版本信息
        case 'version':
          return this.getVersion(originalInstance);

        // 类型信息
        case 'mapType':
          return this.getMapType(originalInstance);

        // 是否已加载
        case 'loaded':
          return this.isLoaded(originalInstance);

        // 容器元素
        case 'container':
          return target.getContainer();

        // 如果属性不存在，提供友好的错误信息
        default:
          throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError("Property or method '".concat(property, "' does not exist on map instance. ") + "Available methods: ".concat(this.getAvailableMethods(target).join(', ')));
      }
    }

    /**
     * 获取版本信息
     */
  }, {
    key: "getVersion",
    value: function getVersion(originalInstance) {
      if (originalInstance && typeof originalInstance.version === 'string') {
        return originalInstance.version;
      }

      // 尝试从全局对象获取版本
      if (typeof window.mapboxgl !== 'undefined') {
        return window.mapboxgl.version || 'unknown';
      }
      if (typeof window.SuperMap3D !== 'undefined') {
        return window.SuperMap3D.VERSION || 'unknown';
      }
      return 'unknown';
    }

    /**
     * 获取地图类型
     */
  }, {
    key: "getMapType",
    value: function getMapType(originalInstance) {
      if (originalInstance) {
        // 检查是否是MapboxGL实例
        if (originalInstance.getStyle || originalInstance.addLayer) {
          return '2d';
        }

        // 检查是否是SuperMap3D实例
        if (originalInstance.scene || originalInstance.camera) {
          return '3d';
        }
      }
      return 'unknown';
    }

    /**
     * 检查是否已加载
     */
  }, {
    key: "isLoaded",
    value: function isLoaded(originalInstance) {
      if (originalInstance) {
        // MapboxGL的loaded状态
        if (typeof originalInstance.loaded === 'function') {
          return originalInstance.loaded();
        }

        // SuperMap3D通常在创建后就是可用的
        if (originalInstance.scene) {
          return true;
        }
      }
      return false;
    }

    /**
     * 获取可用方法列表
     */
  }, {
    key: "getAvailableMethods",
    value: function getAvailableMethods(target) {
      var methods = [];

      // 获取统一接口的方法
      var obj = target;
      while (obj && obj !== Object.prototype) {
        Object.getOwnPropertyNames(obj).forEach(function (name) {
          if (typeof obj[name] === 'function' && !methods.includes(name)) {
            methods.push(name);
          }
        });
        obj = Object.getPrototypeOf(obj);
      }

      // 获取原始实例的方法
      var originalInstance = target.getOriginalInstance();
      if (originalInstance) {
        var originalObj = originalInstance;
        while (originalObj && originalObj !== Object.prototype) {
          Object.getOwnPropertyNames(originalObj).forEach(function (name) {
            if (typeof originalObj[name] === 'function' && !methods.includes(name)) {
              methods.push(name);
            }
          });
          originalObj = Object.getPrototypeOf(originalObj);
        }
      }
      return methods.sort();
    }

    /**
     * 获取代理对象
     */
  }, {
    key: "getProxy",
    value: function getProxy() {
      return this.proxy;
    }

    /**
     * 获取原始地图实例
     */
  }, {
    key: "getMapInstance",
    value: function getMapInstance() {
      return this.mapInstance;
    }

    /**
     * 销毁代理
     */
  }, {
    key: "destroy",
    value: function destroy() {
      if (this.mapInstance) {
        this.mapInstance.remove();
      }
      this.mapInstance = null;
      this.proxy = null;
    }
  }]);
}();

/***/ }),

/***/ "./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MapSDKError: () => (/* binding */ MapSDKError),
/* harmony export */   MapType: () => (/* binding */ MapType)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _wrapNativeSuper(t) { var r = "function" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if ("function" != typeof t) throw new TypeError("Super expression must either be null or a function"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }
function _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf("[native code]"); } catch (n) { return "function" == typeof t; } }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
/**
 * 统一地图SDK类型定义
 */

// 地图类型枚举
var MapType = /*#__PURE__*/function (MapType) {
  MapType["MAP_2D"] = "2d";
  MapType["MAP_3D"] = "3d";
  return MapType;
}({});

// 基础配置接口

// 2D地图配置

// 3D场景配置

// 统一配置类型

// 图层接口

// 数据源接口

// 事件类型

// 地图实例接口

// 错误类型
var MapSDKError = /*#__PURE__*/function (_Error) {
  function MapSDKError(message, code) {
    var _this;
    _classCallCheck(this, MapSDKError);
    _this = _callSuper(this, MapSDKError, [message]);
    _this.code = code;
    _this.name = 'MapSDKError';
    return _this;
  }
  _inherits(MapSDKError, _Error);
  return _createClass(MapSDKError);
}(/*#__PURE__*/_wrapNativeSuper(Error));

// 工厂配置

// 加载状态

/***/ }),

/***/ "./src/utils/loader.ts":
/*!*****************************!*\
  !*** ./src/utils/loader.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ResourceLoader: () => (/* binding */ ResourceLoader)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./src/types/index.ts");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * 动态加载器 - 负责按需加载SDK资源
 */


var ResourceLoader = /*#__PURE__*/function () {
  function ResourceLoader() {
    _classCallCheck(this, ResourceLoader);
    _defineProperty(this, "loadingState", {});
    _defineProperty(this, "loadingPromises", new Map());
    _defineProperty(this, "config", {});
  }
  return _createClass(ResourceLoader, [{
    key: "setConfig",
    value:
    /**
     * 设置SDK资源配置
     */
    function setConfig(config) {
      this.config = _objectSpread(_objectSpread({}, this.config), config);
    }

    /**
     * 加载CSS文件
     */
  }, {
    key: "loadCSS",
    value: function loadCSS(url) {
      return new Promise(function (resolve, reject) {
        // 检查是否已经加载
        var existingLink = document.querySelector("link[href=\"".concat(url, "\"]"));
        if (existingLink) {
          resolve();
          return;
        }
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        link.onload = function () {
          return resolve();
        };
        link.onerror = function () {
          return reject(new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError("Failed to load CSS: ".concat(url)));
        };
        document.head.appendChild(link);
      });
    }

    /**
     * 加载JavaScript文件
     */
  }, {
    key: "loadJS",
    value: function loadJS(url) {
      return new Promise(function (resolve, reject) {
        // 检查是否已经加载
        var existingScript = document.querySelector("script[src=\"".concat(url, "\"]"));
        if (existingScript) {
          resolve();
          return;
        }
        var script = document.createElement('script');
        script.src = url;
        script.type = 'text/javascript';
        script.onload = function () {
          return resolve();
        };
        script.onerror = function () {
          return reject(new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError("Failed to load JS: ".concat(url)));
        };
        document.head.appendChild(script);
      });
    }

    /**
     * 加载MapboxGL
     */
  }, {
    key: "loadMapboxGL",
    value: (function () {
      var _loadMapboxGL2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {
        var cacheKey, loadPromise, _t;
        return _regenerator().w(function (_context) {
          while (1) switch (_context.n) {
            case 0:
              if (!this.loadingState.mapboxgl) {
                _context.n = 1;
                break;
              }
              return _context.a(2);
            case 1:
              cacheKey = 'mapboxgl';
              if (!this.loadingPromises.has(cacheKey)) {
                _context.n = 2;
                break;
              }
              return _context.a(2, this.loadingPromises.get(cacheKey));
            case 2:
              loadPromise = this._loadMapboxGL();
              this.loadingPromises.set(cacheKey, loadPromise);
              _context.p = 3;
              _context.n = 4;
              return loadPromise;
            case 4:
              this.loadingState.mapboxgl = true;
              _context.n = 6;
              break;
            case 5:
              _context.p = 5;
              _t = _context.v;
              this.loadingPromises.delete(cacheKey);
              throw _t;
            case 6:
              return _context.a(2);
          }
        }, _callee, this, [[3, 5]]);
      }));
      function loadMapboxGL() {
        return _loadMapboxGL2.apply(this, arguments);
      }
      return loadMapboxGL;
    }())
  }, {
    key: "_loadMapboxGL",
    value: function () {
      var _loadMapboxGL3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {
        var config;
        return _regenerator().w(function (_context2) {
          while (1) switch (_context2.n) {
            case 0:
              config = this.config.mapboxgl;
              if (config) {
                _context2.n = 1;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('MapboxGL configuration not found');
            case 1:
              if (!(typeof window.mapboxgl !== 'undefined')) {
                _context2.n = 2;
                break;
              }
              return _context2.a(2);
            case 2:
              _context2.n = 3;
              return Promise.all([config.css ? this.loadCSS(config.css) : Promise.resolve(), this.loadJS(config.js)]);
            case 3:
              if (!(typeof window.mapboxgl === 'undefined')) {
                _context2.n = 4;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('MapboxGL failed to load properly');
            case 4:
              return _context2.a(2);
          }
        }, _callee2, this);
      }));
      function _loadMapboxGL() {
        return _loadMapboxGL3.apply(this, arguments);
      }
      return _loadMapboxGL;
    }()
    /**
     * 加载SuperMap3D
     */
  }, {
    key: "loadSuperMap3D",
    value: (function () {
      var _loadSuperMap3D2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {
        var cacheKey, loadPromise, _t2;
        return _regenerator().w(function (_context3) {
          while (1) switch (_context3.n) {
            case 0:
              if (!this.loadingState.supermap3d) {
                _context3.n = 1;
                break;
              }
              return _context3.a(2);
            case 1:
              cacheKey = 'supermap3d';
              if (!this.loadingPromises.has(cacheKey)) {
                _context3.n = 2;
                break;
              }
              return _context3.a(2, this.loadingPromises.get(cacheKey));
            case 2:
              loadPromise = this._loadSuperMap3D();
              this.loadingPromises.set(cacheKey, loadPromise);
              _context3.p = 3;
              _context3.n = 4;
              return loadPromise;
            case 4:
              this.loadingState.supermap3d = true;
              _context3.n = 6;
              break;
            case 5:
              _context3.p = 5;
              _t2 = _context3.v;
              this.loadingPromises.delete(cacheKey);
              throw _t2;
            case 6:
              return _context3.a(2);
          }
        }, _callee3, this, [[3, 5]]);
      }));
      function loadSuperMap3D() {
        return _loadSuperMap3D2.apply(this, arguments);
      }
      return loadSuperMap3D;
    }())
  }, {
    key: "_loadSuperMap3D",
    value: function () {
      var _loadSuperMap3D3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {
        var config, loadTasks;
        return _regenerator().w(function (_context4) {
          while (1) switch (_context4.n) {
            case 0:
              config = this.config.supermap3d;
              if (config) {
                _context4.n = 1;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('SuperMap3D configuration not found');
            case 1:
              if (!(typeof window.SuperMap3D !== 'undefined')) {
                _context4.n = 2;
                break;
              }
              return _context4.a(2);
            case 2:
              // 加载资源
              loadTasks = [this.loadJS(config.js)];
              if (config.css) {
                loadTasks.push(this.loadCSS(config.css));
              }
              _context4.n = 3;
              return Promise.all(loadTasks);
            case 3:
              if (!(typeof window.SuperMap3D === 'undefined')) {
                _context4.n = 4;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('SuperMap3D failed to load properly');
            case 4:
              return _context4.a(2);
          }
        }, _callee4, this);
      }));
      function _loadSuperMap3D() {
        return _loadSuperMap3D3.apply(this, arguments);
      }
      return _loadSuperMap3D;
    }()
    /**
     * 加载iClient for MapboxGL
     */
  }, {
    key: "loadIClientMapboxGL",
    value: (function () {
      var _loadIClientMapboxGL2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {
        var cacheKey, loadPromise, _t3;
        return _regenerator().w(function (_context5) {
          while (1) switch (_context5.n) {
            case 0:
              if (!this.loadingState.iclientMapboxgl) {
                _context5.n = 1;
                break;
              }
              return _context5.a(2);
            case 1:
              cacheKey = 'iclientMapboxgl';
              if (!this.loadingPromises.has(cacheKey)) {
                _context5.n = 2;
                break;
              }
              return _context5.a(2, this.loadingPromises.get(cacheKey));
            case 2:
              loadPromise = this._loadIClientMapboxGL();
              this.loadingPromises.set(cacheKey, loadPromise);
              _context5.p = 3;
              _context5.n = 4;
              return loadPromise;
            case 4:
              this.loadingState.iclientMapboxgl = true;
              _context5.n = 6;
              break;
            case 5:
              _context5.p = 5;
              _t3 = _context5.v;
              this.loadingPromises.delete(cacheKey);
              throw _t3;
            case 6:
              return _context5.a(2);
          }
        }, _callee5, this, [[3, 5]]);
      }));
      function loadIClientMapboxGL() {
        return _loadIClientMapboxGL2.apply(this, arguments);
      }
      return loadIClientMapboxGL;
    }())
  }, {
    key: "_loadIClientMapboxGL",
    value: function () {
      var _loadIClientMapboxGL3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {
        var config, mapboxgl;
        return _regenerator().w(function (_context6) {
          while (1) switch (_context6.n) {
            case 0:
              config = this.config.iclientMapboxgl;
              if (config) {
                _context6.n = 1;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('iClient MapboxGL configuration not found');
            case 1:
              _context6.n = 2;
              return this.loadMapboxGL();
            case 2:
              // 检查iClient是否已加载
              mapboxgl = window.mapboxgl;
              if (!(mapboxgl && mapboxgl.supermap)) {
                _context6.n = 3;
                break;
              }
              return _context6.a(2);
            case 3:
              _context6.n = 4;
              return Promise.all([this.loadCSS(config.css), this.loadJS(config.js)]);
            case 4:
              if (!(!mapboxgl || !mapboxgl.supermap)) {
                _context6.n = 5;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('iClient for MapboxGL failed to load properly');
            case 5:
              return _context6.a(2);
          }
        }, _callee6, this);
      }));
      function _loadIClientMapboxGL() {
        return _loadIClientMapboxGL3.apply(this, arguments);
      }
      return _loadIClientMapboxGL;
    }()
    /**
     * 加载iClient3D
     */
  }, {
    key: "loadIClient3D",
    value: (function () {
      var _loadIClient3D2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7() {
        var cacheKey, loadPromise, _t4;
        return _regenerator().w(function (_context7) {
          while (1) switch (_context7.n) {
            case 0:
              if (!this.loadingState.iclient3d) {
                _context7.n = 1;
                break;
              }
              return _context7.a(2);
            case 1:
              cacheKey = 'iclient3d';
              if (!this.loadingPromises.has(cacheKey)) {
                _context7.n = 2;
                break;
              }
              return _context7.a(2, this.loadingPromises.get(cacheKey));
            case 2:
              loadPromise = this._loadIClient3D();
              this.loadingPromises.set(cacheKey, loadPromise);
              _context7.p = 3;
              _context7.n = 4;
              return loadPromise;
            case 4:
              this.loadingState.iclient3d = true;
              _context7.n = 6;
              break;
            case 5:
              _context7.p = 5;
              _t4 = _context7.v;
              this.loadingPromises.delete(cacheKey);
              throw _t4;
            case 6:
              return _context7.a(2);
          }
        }, _callee7, this, [[3, 5]]);
      }));
      function loadIClient3D() {
        return _loadIClient3D2.apply(this, arguments);
      }
      return loadIClient3D;
    }())
  }, {
    key: "_loadIClient3D",
    value: function () {
      var _loadIClient3D3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {
        var config, SuperMap3D;
        return _regenerator().w(function (_context8) {
          while (1) switch (_context8.n) {
            case 0:
              config = this.config.iclient3d;
              if (config) {
                _context8.n = 1;
                break;
              }
              throw new _types__WEBPACK_IMPORTED_MODULE_0__.MapSDKError('iClient3D configuration not found');
            case 1:
              _context8.n = 2;
              return this.loadSuperMap3D();
            case 2:
              // 检查iClient3D是否已加载（通过检查SuperMap3D的扩展）
              SuperMap3D = window.SuperMap3D;
              if (!(SuperMap3D && SuperMap3D.iClient)) {
                _context8.n = 3;
                break;
              }
              return _context8.a(2);
            case 3:
              _context8.n = 4;
              return this.loadJS(config.js);
            case 4:
              // 验证加载结果（这里可能需要根据实际的iClient3D结构调整）
              if (!SuperMap3D || !SuperMap3D.iClient) {
                console.warn('iClient3D validation may need adjustment based on actual structure');
              }
            case 5:
              return _context8.a(2);
          }
        }, _callee8, this);
      }));
      function _loadIClient3D() {
        return _loadIClient3D3.apply(this, arguments);
      }
      return _loadIClient3D;
    }()
    /**
     * 获取加载状态
     */
  }, {
    key: "getLoadingState",
    value: function getLoadingState() {
      return _objectSpread({}, this.loadingState);
    }

    /**
     * 重置加载状态（用于测试）
     */
  }, {
    key: "reset",
    value: function reset() {
      this.loadingState = {};
      this.loadingPromises.clear();
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      if (!ResourceLoader.instance) {
        ResourceLoader.instance = new ResourceLoader();
      }
      return ResourceLoader.instance;
    }
  }]);
}();

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!**********************!*\
  !*** ./src/index.ts ***!
  \**********************/
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports SGMapSDK, createMap, create2DMap, create3DMap, createAutoMap */
/* harmony import */ var _core_map_factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/map-factory */ "./src/core/map-factory.ts");
/* harmony import */ var _core_map_proxy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core/map-proxy */ "./src/core/map-proxy.ts");
/* harmony import */ var _utils_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/loader */ "./src/utils/loader.ts");
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./types */ "./src/types/index.ts");
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * 统一地图SDK主入口
 */






/**
 * 统一地图SDK类
 */
var SGMapSDK = /*#__PURE__*/function () {
  function SGMapSDK() {
    _classCallCheck(this, SGMapSDK);
  }
  return _createClass(SGMapSDK, null, [{
    key: "setConfig",
    value:
    /**
     * 设置SDK资源配置
     */
    function setConfig(config) {
      this.factory.setConfig(config);
    }

    /**
     * 创建地图实例
     */
  }, {
    key: "createMap",
    value: (function () {
      var _createMap = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(config) {
        var mapInstance, proxy, _t;
        return _regenerator().w(function (_context) {
          while (1) switch (_context.n) {
            case 0:
              _context.p = 0;
              _context.n = 1;
              return this.factory.createMap(config);
            case 1:
              mapInstance = _context.v;
              proxy = new _core_map_proxy__WEBPACK_IMPORTED_MODULE_1__.MapProxy(mapInstance);
              return _context.a(2, proxy.getProxy());
            case 2:
              _context.p = 2;
              _t = _context.v;
              if (!(_t instanceof _types__WEBPACK_IMPORTED_MODULE_3__.MapSDKError)) {
                _context.n = 3;
                break;
              }
              throw _t;
            case 3:
              throw new _types__WEBPACK_IMPORTED_MODULE_3__.MapSDKError("Failed to create map: ".concat(_t.message));
            case 4:
              return _context.a(2);
          }
        }, _callee, this, [[0, 2]]);
      }));
      function createMap(_x) {
        return _createMap.apply(this, arguments);
      }
      return createMap;
    }()
    /**
     * 获取加载状态
     */
    )
  }, {
    key: "getLoadingState",
    value: function getLoadingState() {
      return this.factory.getLoadingState();
    }

    /**
     * 预加载SDK资源
     */
  }, {
    key: "preloadSDK",
    value: (function () {
      var _preloadSDK = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {
        var type,
          loadTasks,
          _args2 = arguments;
        return _regenerator().w(function (_context2) {
          while (1) switch (_context2.n) {
            case 0:
              type = _args2.length > 0 && _args2[0] !== undefined ? _args2[0] : 'all';
              loadTasks = [];
              if (type === 'all' || type === '2d') {
                loadTasks.push(this.loader.loadMapboxGL(), this.loader.loadIClientMapboxGL());
              }
              if (type === 'all' || type === '3d') {
                loadTasks.push(this.loader.loadSuperMap3D(), this.loader.loadIClient3D());
              }
              _context2.n = 1;
              return Promise.all(loadTasks);
            case 1:
              return _context2.a(2);
          }
        }, _callee2, this);
      }));
      function preloadSDK() {
        return _preloadSDK.apply(this, arguments);
      }
      return preloadSDK;
    }()
    /**
     * 检查SDK是否已加载
     */
    )
  }, {
    key: "isSDKLoaded",
    value: function isSDKLoaded(type) {
      var state = this.getLoadingState();
      if (type === '2d') {
        return !!(state.mapboxgl && state.iclientMapboxgl);
      } else {
        return !!(state.supermap3d && state.iclient3d);
      }
    }

    /**
     * 获取版本信息
     */
  }, {
    key: "getVersion",
    value: function getVersion() {
      return '1.0.0';
    }
  }]);
}();

/**
 * 便捷的创建地图函数
 */
_defineProperty(SGMapSDK, "factory", _core_map_factory__WEBPACK_IMPORTED_MODULE_0__.MapFactory.getInstance());
_defineProperty(SGMapSDK, "loader", _utils_loader__WEBPACK_IMPORTED_MODULE_2__.ResourceLoader.getInstance());
function createMap(_x2) {
  return _createMap2.apply(this, arguments);
}

/**
 * 便捷的2D地图创建函数
 */
function _createMap2() {
  _createMap2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(config) {
    return _regenerator().w(function (_context3) {
      while (1) switch (_context3.n) {
        case 0:
          return _context3.a(2, SGMapSDK.createMap(config));
      }
    }, _callee3);
  }));
  return _createMap2.apply(this, arguments);
}
function create2DMap(_x3) {
  return _create2DMap.apply(this, arguments);
}

/**
 * 便捷的3D地图创建函数
 */
function _create2DMap() {
  _create2DMap = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(config) {
    return _regenerator().w(function (_context4) {
      while (1) switch (_context4.n) {
        case 0:
          return _context4.a(2, SGMapSDK.createMap(_objectSpread(_objectSpread({}, config), {}, {
            type: _types__WEBPACK_IMPORTED_MODULE_3__.MapType.MAP_2D
          })));
      }
    }, _callee4);
  }));
  return _create2DMap.apply(this, arguments);
}
function create3DMap(_x4) {
  return _create3DMap.apply(this, arguments);
}

/**
 * 自动检测地图类型并创建
 */
function _create3DMap() {
  _create3DMap = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5(config) {
    return _regenerator().w(function (_context5) {
      while (1) switch (_context5.n) {
        case 0:
          return _context5.a(2, SGMapSDK.createMap(_objectSpread(_objectSpread({}, config), {}, {
            type: _types__WEBPACK_IMPORTED_MODULE_3__.MapType.MAP_3D
          })));
      }
    }, _callee5);
  }));
  return _create3DMap.apply(this, arguments);
}
function createAutoMap(_x5) {
  return _createAutoMap.apply(this, arguments);
}

// 导出类型和接口
function _createAutoMap() {
  _createAutoMap = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6(config) {
    return _regenerator().w(function (_context6) {
      while (1) switch (_context6.n) {
        case 0:
          return _context6.a(2, SGMapSDK.createMap(config));
      }
    }, _callee6);
  }));
  return _createAutoMap.apply(this, arguments);
}


// 导出核心类


// 默认导出
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SGMapSDK);

// 全局对象挂载（用于script标签引入）
if (typeof window !== 'undefined') {
  window.SGMapSDK = SGMapSDK;
  window.createMap = createMap;
  window.create2DMap = create2DMap;
  window.create3DMap = create3DMap;
  window.createAutoMap = createAutoMap;
}
})();

__webpack_exports__ = __webpack_exports__["default"];
/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=sg-map-sdk.debug.js.map