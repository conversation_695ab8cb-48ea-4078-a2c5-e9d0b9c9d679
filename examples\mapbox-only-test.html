<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MapboxGL 独立测试</title>

    <!-- 直接加载 MapboxGL -->
    <link href="../iclient-mapboxgl/mapbox-gl.css" rel="stylesheet">
    <script src="../iclient-mapboxgl/mapbox-gl.js"></script>

    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }

        #map {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px 0;
        }

        .controls {
            margin: 10px 0;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>MapboxGL 独立测试</h1>

    <div class="controls">
        <button onclick="testMapboxGL()">测试 MapboxGL</button>
        <button onclick="createSimpleMap()">创建 OSM 地图</button>
        <button onclick="createCartoMap()">创建 Carto 地图</button>
        <button onclick="createStamenMap()">创建 Stamen 地图</button>
        <button onclick="clearMap()">清除地图</button>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <div id="map"></div>

    <div class="log" id="log"></div>

    <script>
        let currentMap = null;

        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logEl.innerHTML += `<span class="${className}">[${time}] ${message}</span>\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[${time}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testMapboxGL() {
            log('=== MapboxGL 测试开始 ===');

            if (typeof window.mapboxgl !== 'undefined') {
                log(`✓ MapboxGL 已加载`, 'success');
                log(`版本: ${window.mapboxgl.version || 'unknown'}`);
                log(`支持的功能: ${JSON.stringify({
                    supported: window.mapboxgl.supported(),
                    workerCount: window.mapboxgl.workerCount || 'unknown'
                })}`);
            } else {
                log('✗ MapboxGL 未加载', 'error');
                return;
            }

            log('=== MapboxGL 测试完成 ===');
        }

        async function createSimpleMap() {
            log('=== 创建简单地图测试开始 ===');

            try {
                if (typeof window.mapboxgl === 'undefined') {
                    throw new Error('MapboxGL 未加载');
                }

                // 清除现有地图
                if (currentMap) {
                    currentMap.remove();
                    currentMap = null;
                    log('已清除现有地图');
                }

                log('开始创建 MapboxGL 地图...');

                // 使用 OpenStreetMap 样式的地图配置
                const mapConfig = {
                    container: 'map',
                    center: [116.3974, 39.9093], // 北京
                    zoom: 10,
                    style: {
                        version: 8,
                        sources: {
                            'osm-tiles': {
                                type: 'raster',
                                tiles: [
                                    'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
                                ],
                                tileSize: 256,
                                attribution: '© OpenStreetMap contributors'
                            }
                        },
                        layers: [
                            {
                                id: 'osm-tiles-layer',
                                type: 'raster',
                                source: 'osm-tiles',
                                minzoom: 0,
                                maxzoom: 19
                            }
                        ]
                    }
                };

                log(`地图配置: ${JSON.stringify(mapConfig, null, 2)}`);

                // 创建地图实例
                currentMap = new window.mapboxgl.Map(mapConfig);

                // 监听地图事件
                currentMap.on('load', () => {
                    log('✓ 地图加载完成！', 'success');
                    log(`地图中心: [${currentMap.getCenter().lng.toFixed(4)}, ${currentMap.getCenter().lat.toFixed(4)}]`);
                    log(`缩放级别: ${currentMap.getZoom().toFixed(2)}`);
                });

                currentMap.on('error', (e) => {
                    log(`✗ 地图错误: ${e.error?.message || 'Unknown error'}`, 'error');
                });

                currentMap.on('click', (e) => {
                    log(`地图被点击: [${e.lngLat.lng.toFixed(4)}, ${e.lngLat.lat.toFixed(4)}]`);
                });

                log('地图实例已创建，等待加载完成...');

            } catch (error) {
                log(`✗ 创建地图失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`);
            }

            log('=== 创建简单地图测试完成 ===');
        }

        async function createCartoMap() {
            log('=== 创建 Carto 地图测试开始 ===');

            try {
                if (typeof window.mapboxgl === 'undefined') {
                    throw new Error('MapboxGL 未加载');
                }

                // 清除现有地图
                if (currentMap) {
                    currentMap.remove();
                    currentMap = null;
                    log('已清除现有地图');
                }

                log('开始创建 Carto 地图...');

                // 使用 Carto 样式的地图配置
                const mapConfig = {
                    container: 'map',
                    center: [116.3974, 39.9093], // 北京
                    zoom: 10,
                    style: {
                        version: 8,
                        sources: {
                            'carto-tiles': {
                                type: 'raster',
                                tiles: [
                                    'https://cartodb-basemaps-a.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png'
                                ],
                                tileSize: 256,
                                attribution: '© CARTO © OpenStreetMap contributors'
                            }
                        },
                        layers: [
                            {
                                id: 'carto-tiles-layer',
                                type: 'raster',
                                source: 'carto-tiles',
                                minzoom: 0,
                                maxzoom: 19
                            }
                        ]
                    }
                };

                log(`地图配置: Carto Light 样式`);

                // 创建地图实例
                currentMap = new window.mapboxgl.Map(mapConfig);

                // 监听地图事件
                currentMap.on('load', () => {
                    log('✓ Carto 地图加载完成！', 'success');
                    log(`地图中心: [${currentMap.getCenter().lng.toFixed(4)}, ${currentMap.getCenter().lat.toFixed(4)}]`);
                    log(`缩放级别: ${currentMap.getZoom().toFixed(2)}`);
                });

                currentMap.on('error', (e) => {
                    log(`✗ 地图错误: ${e.error?.message || 'Unknown error'}`, 'error');
                });

                log('Carto 地图实例已创建，等待加载完成...');

            } catch (error) {
                log(`✗ 创建 Carto 地图失败: ${error.message}`, 'error');
            }

            log('=== 创建 Carto 地图测试完成 ===');
        }

        async function createStamenMap() {
            log('=== 创建 Stamen 地图测试开始 ===');

            try {
                if (typeof window.mapboxgl === 'undefined') {
                    throw new Error('MapboxGL 未加载');
                }

                // 清除现有地图
                if (currentMap) {
                    currentMap.remove();
                    currentMap = null;
                    log('已清除现有地图');
                }

                log('开始创建 Stamen 地图...');

                // 使用 Stamen 样式的地图配置
                const mapConfig = {
                    container: 'map',
                    center: [116.3974, 39.9093], // 北京
                    zoom: 10,
                    style: {
                        version: 8,
                        sources: {
                            'stamen-tiles': {
                                type: 'raster',
                                tiles: [
                                    'https://stamen-tiles.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png'
                                ],
                                tileSize: 256,
                                attribution: 'Map tiles by Stamen Design, under CC BY 3.0. Data by OpenStreetMap, under ODbL.'
                            }
                        },
                        layers: [
                            {
                                id: 'stamen-tiles-layer',
                                type: 'raster',
                                source: 'stamen-tiles',
                                minzoom: 0,
                                maxzoom: 18
                            }
                        ]
                    }
                };

                log(`地图配置: Stamen Terrain 样式`);

                // 创建地图实例
                currentMap = new window.mapboxgl.Map(mapConfig);

                // 监听地图事件
                currentMap.on('load', () => {
                    log('✓ Stamen 地图加载完成！', 'success');
                    log(`地图中心: [${currentMap.getCenter().lng.toFixed(4)}, ${currentMap.getCenter().lat.toFixed(4)}]`);
                    log(`缩放级别: ${currentMap.getZoom().toFixed(2)}`);
                });

                currentMap.on('error', (e) => {
                    log(`✗ 地图错误: ${e.error?.message || 'Unknown error'}`, 'error');
                });

                log('Stamen 地图实例已创建，等待加载完成...');

            } catch (error) {
                log(`✗ 创建 Stamen 地图失败: ${error.message}`, 'error');
            }

            log('=== 创建 Stamen 地图测试完成 ===');
        }

        function clearMap() {
            if (currentMap) {
                currentMap.remove();
                currentMap = null;
                log('✓ 地图已清除', 'success');
            } else {
                log('没有地图需要清除');
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            testMapboxGL();
        });
    </script>
</body>
</html>
