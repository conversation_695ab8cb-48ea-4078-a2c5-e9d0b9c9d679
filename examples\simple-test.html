<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一地图SDK简单测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .controls {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .controls button {
            padding: 10px 15px;
            margin-right: 10px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #005a87;
        }
        
        .controls button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        #map {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        
        .status {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .log-entry {
            margin-bottom: 5px;
        }
        
        .log-entry.success {
            color: #27ae60;
        }
        
        .log-entry.error {
            color: #e74c3c;
        }
        
        .log-entry.info {
            color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>统一地图SDK简单测试</h1>
            <p>测试SDK的基本功能和API</p>
        </div>
        
        <div class="controls">
            <button id="testSDK">测试SDK加载</button>
            <button id="testAPI">测试API</button>
            <button id="testConfig">测试配置</button>
            <button id="clearLog">清除日志</button>
        </div>
        
        <div id="map">
            <div>地图容器 - 等待初始化</div>
        </div>
        
        <div class="status">
            <h3>状态信息</h3>
            <div>SDK状态: <span id="sdkStatus">检查中...</span></div>
            <div>版本信息: <span id="versionInfo">-</span></div>
            <div>可用功能: <span id="availableFeatures">-</span></div>
            
            <div class="log" id="logContainer">
                <div class="log-entry info">等待测试...</div>
            </div>
        </div>
    </div>

    <!-- 引入构建的SDK -->
    <script src="../dist/sg-map-sdk.umd.js"></script>
    
    <script>
        // 日志记录
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新状态
        function updateStatus() {
            const sdkStatus = document.getElementById('sdkStatus');
            const versionInfo = document.getElementById('versionInfo');
            const availableFeatures = document.getElementById('availableFeatures');
            
            if (typeof window.SGMapSDK !== 'undefined') {
                sdkStatus.textContent = '已加载';
                sdkStatus.style.color = '#27ae60';
                
                try {
                    versionInfo.textContent = window.SGMapSDK.getVersion();
                } catch (error) {
                    versionInfo.textContent = '获取失败';
                }
                
                const features = [];
                if (typeof window.createMap === 'function') features.push('createMap');
                if (typeof window.create2DMap === 'function') features.push('create2DMap');
                if (typeof window.create3DMap === 'function') features.push('create3DMap');
                if (typeof window.createAutoMap === 'function') features.push('createAutoMap');
                
                availableFeatures.textContent = features.join(', ') || '无';
            } else {
                sdkStatus.textContent = '未加载';
                sdkStatus.style.color = '#e74c3c';
                versionInfo.textContent = '-';
                availableFeatures.textContent = '-';
            }
        }

        // 测试SDK加载
        function testSDK() {
            log('开始测试SDK加载...', 'info');
            
            try {
                // 检查全局对象
                if (typeof window.SGMapSDK === 'undefined') {
                    log('错误: SGMapSDK全局对象不存在', 'error');
                    return;
                }
                
                log('✓ SGMapSDK全局对象存在', 'success');
                
                // 检查静态方法
                const methods = ['setConfig', 'createMap', 'getLoadingState', 'preloadSDK', 'isSDKLoaded', 'getVersion'];
                methods.forEach(method => {
                    if (typeof window.SGMapSDK[method] === 'function') {
                        log(`✓ SGMapSDK.${method} 方法存在`, 'success');
                    } else {
                        log(`✗ SGMapSDK.${method} 方法不存在`, 'error');
                    }
                });
                
                // 检查便捷函数
                const functions = ['createMap', 'create2DMap', 'create3DMap', 'createAutoMap'];
                functions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        log(`✓ ${func} 全局函数存在`, 'success');
                    } else {
                        log(`✗ ${func} 全局函数不存在`, 'error');
                    }
                });
                
                log('SDK加载测试完成', 'success');
                
            } catch (error) {
                log(`SDK测试失败: ${error.message}`, 'error');
            }
        }

        // 测试API
        function testAPI() {
            log('开始测试API...', 'info');
            
            try {
                // 测试版本获取
                const version = window.SGMapSDK.getVersion();
                log(`✓ 版本信息: ${version}`, 'success');
                
                // 测试加载状态
                const loadingState = window.SGMapSDK.getLoadingState();
                log(`✓ 加载状态: ${JSON.stringify(loadingState)}`, 'success');
                
                // 测试SDK检查
                const is2DLoaded = window.SGMapSDK.isSDKLoaded('2d');
                const is3DLoaded = window.SGMapSDK.isSDKLoaded('3d');
                log(`✓ 2D SDK状态: ${is2DLoaded}`, 'success');
                log(`✓ 3D SDK状态: ${is3DLoaded}`, 'success');
                
                log('API测试完成', 'success');
                
            } catch (error) {
                log(`API测试失败: ${error.message}`, 'error');
            }
        }

        // 测试配置
        function testConfig() {
            log('开始测试配置...', 'info');
            
            try {
                // 测试配置设置
                const testConfig = {
                    mapboxgl: {
                        js: 'https://api.mapbox.com/mapbox-gl-js/v1.13.2/mapbox-gl.js',
                        css: 'https://api.mapbox.com/mapbox-gl-js/v1.13.2/mapbox-gl.css'
                    }
                };
                
                window.SGMapSDK.setConfig(testConfig);
                log('✓ 配置设置成功', 'success');
                
                // 测试地图创建配置（不实际创建）
                const mapConfig = {
                    container: 'map',
                    center: [116.3974, 39.9093],
                    zoom: 10
                };
                
                log(`✓ 地图配置验证: ${JSON.stringify(mapConfig)}`, 'success');
                
                log('配置测试完成', 'success');
                
            } catch (error) {
                log(`配置测试失败: ${error.message}`, 'error');
            }
        }

        // 清除日志
        function clearLog() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry info">日志已清除</div>';
        }

        // 绑定事件
        document.getElementById('testSDK').addEventListener('click', testSDK);
        document.getElementById('testAPI').addEventListener('click', testAPI);
        document.getElementById('testConfig').addEventListener('click', testConfig);
        document.getElementById('clearLog').addEventListener('click', clearLog);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成', 'info');
            updateStatus();
            
            // 自动运行基本测试
            setTimeout(() => {
                log('自动运行基本测试...', 'info');
                testSDK();
            }, 500);
        });
    </script>
</body>
</html>
