const { merge } = require('webpack-merge');
const common = require('./webpack.common.js');
const path = require('path');

module.exports = merge(common, {
  mode: 'development',

  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'sg-map-sdk.debug.js',
    library: {
      name: 'SGMapSDK',
      type: 'umd',
      export: 'default'
    },
    globalObject: 'this',
    clean: false,
  },

  // 生成详细的 source map 用于调试
  devtool: 'source-map',

  optimization: {
    // 禁用压缩和混淆
    minimize: false,
    // 保持可读的模块名
    moduleIds: 'named',
    chunkIds: 'named',
    // 不进行代码分割，保持单文件
    splitChunks: false,
    // 保持函数名和变量名
    mangleExports: false,
  },
});
