<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径调试测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>路径调试测试</h1>
    
    <button onclick="testPaths()">测试文件路径</button>
    <button onclick="clearLog()">清除日志</button>
    
    <div class="log" id="log"></div>

    <script>
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logEl.innerHTML += `<span class="${className}">[${time}] ${message}</span>\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[${time}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testFileAccess(url, description) {
            try {
                const response = await fetch(url);
                if (response.ok) {
                    const size = response.headers.get('content-length') || 'unknown';
                    log(`✓ ${description}: 可访问 (大小: ${size} bytes)`, 'success');
                    return true;
                } else {
                    log(`✗ ${description}: HTTP ${response.status} ${response.statusText}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`✗ ${description}: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testPaths() {
            log('=== 开始测试文件路径 ===');
            
            const paths = [
                { url: '../dist/sg-map-sdk.debug.js', desc: 'SDK调试文件' },
                { url: '../iclient-mapboxgl/mapbox-gl.js', desc: 'MapboxGL JS' },
                { url: '../iclient-mapboxgl/mapbox-gl.css', desc: 'MapboxGL CSS' },
                { url: '../iclient-mapboxgl/iclient-mapboxgl.js', desc: 'iClient MapboxGL JS' },
                { url: '../iclient-mapboxgl/iclient-mapboxgl.css', desc: 'iClient MapboxGL CSS' },
                { url: '../iclient-3d/SuperMap3D/SuperMap3D.js', desc: 'SuperMap3D JS' },
                { url: '../iclient-3d/SuperMap3D/Widgets/widgets.css', desc: 'SuperMap3D CSS' },
                { url: '../iclient-3d/deps.js', desc: 'iClient3D deps' }
            ];
            
            let successCount = 0;
            for (const path of paths) {
                const success = await testFileAccess(path.url, path.desc);
                if (success) successCount++;
            }
            
            log(`=== 测试完成: ${successCount}/${paths.length} 文件可访问 ===`);
            
            if (successCount === paths.length) {
                log('所有文件都可以正常访问！', 'success');
            } else {
                log('部分文件无法访问，请检查路径配置', 'error');
            }
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，点击按钮开始测试');
        });
    </script>
</body>
</html>
