/*! For license information please see sg-map-sdk.umd.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.SGMapSDK=t():e.SGMapSDK=t()}(this,(()=>(()=>{"use strict";var e={d:(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},t={};function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e){var t="function"==typeof Map?new Map:void 0;return r=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return function(e,t,n){if(o())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,t);var a=new(e.bind.apply(e,r));return n&&i(a,n.prototype),a}(e,arguments,a(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),i(n,e)},r(e)}function o(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(o=function(){return!!e})()}function i(e,t){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},i(e,t)}function a(e){return a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},a(e)}e.d(t,{default:()=>ge});var c=function(e){return e.MAP_2D="2d",e.MAP_3D="3d",e}({}),u=function(e){function t(e,r){var i;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(i=function(e,t,r){return t=a(t),function(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,o()?Reflect.construct(t,r||[],a(e).constructor):t.apply(e,r))}(this,t,[e])).code=r,i.name="MapSDKError",i}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&i(e,t)}(t,e),r=t,Object.defineProperty(r,"prototype",{writable:!1}),r;var r}(r(Error));function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function f(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,s=Object.create(u.prototype);return l(s,"_invoke",function(n,r,o){var i,c,u,s=0,f=o||[],l=!1,p={p:0,n:0,v:e,a:y,f:y.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function y(n,r){for(c=n,u=r,t=0;!l&&s&&!o&&t<f.length;t++){var o,i=f[t],y=p.p,h=i[2];n>3?(o=h===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=y&&((o=n<2&&y<i[1])?(c=0,p.v=r,p.n=i[1]):y<h&&(o=n<3||i[0]>r||r>h)&&(i[4]=n,i[5]=r,p.n=h,c=0))}if(o||n>1)return a;throw l=!0,r}return function(o,f,h){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,h),c=f,u=h;(t=c<2?e:u)||!l;){i||(c?c<3?(c>1&&(p.n=-1),y(c,u)):p.n=u:p.v=u);try{if(s=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(l=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{s=1}}return{value:t,done:l}}}(n,o,i),!0),s}var a={};function c(){}function u(){}function s(){}t=Object.getPrototypeOf;var p=[][r]?t(t([][r]())):(l(t={},r,(function(){return this})),t),y=s.prototype=c.prototype=Object.create(p);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,l(e,o,"GeneratorFunction")),e.prototype=Object.create(y),e}return u.prototype=s,l(y,"constructor",s),l(s,"constructor",u),u.displayName="GeneratorFunction",l(s,o,"GeneratorFunction"),l(y),l(y,o,"Generator"),l(y,r,(function(){return this})),l(y,"toString",(function(){return"[object Generator]"})),(f=function(){return{w:i,m:h}})()}function l(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}l=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){l(e,t,(function(e){return this._invoke(t,n,e)}))};i("next",0),i("throw",1),i("return",2)}},l(e,t,n,r)}function p(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function y(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){p(i,r,o,a,c,"next",e)}function c(e){p(i,r,o,a,c,"throw",e)}a(void 0)}))}}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,m(r.key),r)}}function b(e,t,n){return(t=m(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=function(e){if("object"!=s(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==s(t)?t:t+""}var g=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),b(this,"loadingState",{}),b(this,"loadingPromises",new Map),b(this,"config",{})}return t=e,n=[{key:"setConfig",value:function(e){this.config=d(d({},this.config),e)}},{key:"loadCSS",value:function(e){return new Promise((function(t,n){if(document.querySelector('link[href="'.concat(e,'"]')))t();else{var r=document.createElement("link");r.rel="stylesheet",r.href=e,r.onload=function(){return t()},r.onerror=function(){return n(new u("Failed to load CSS: ".concat(e)))},document.head.appendChild(r)}}))}},{key:"loadJS",value:function(e){return new Promise((function(t,n){if(document.querySelector('script[src="'.concat(e,'"]')))t();else{var r=document.createElement("script");r.src=e,r.type="text/javascript",r.onload=function(){return t()},r.onerror=function(){return n(new u("Failed to load JS: ".concat(e)))},document.head.appendChild(r)}}))}},{key:"loadMapboxGL",value:(h=y(f().m((function e(){var t,n,r;return f().w((function(e){for(;;)switch(e.n){case 0:if(!this.loadingState.mapboxgl){e.n=1;break}return e.a(2);case 1:if(t="mapboxgl",!this.loadingPromises.has(t)){e.n=2;break}return e.a(2,this.loadingPromises.get(t));case 2:return n=this._loadMapboxGL(),this.loadingPromises.set(t,n),e.p=3,e.n=4,n;case 4:this.loadingState.mapboxgl=!0,e.n=6;break;case 5:throw e.p=5,r=e.v,this.loadingPromises.delete(t),r;case 6:return e.a(2)}}),e,this,[[3,5]])}))),function(){return h.apply(this,arguments)})},{key:"_loadMapboxGL",value:(p=y(f().m((function e(){var t;return f().w((function(e){for(;;)switch(e.n){case 0:if(t=this.config.mapboxgl){e.n=1;break}throw new u("MapboxGL configuration not found");case 1:if(void 0===window.mapboxgl){e.n=2;break}return e.a(2);case 2:return e.n=3,Promise.all([t.css?this.loadCSS(t.css):Promise.resolve(),this.loadJS(t.js)]);case 3:if(void 0!==window.mapboxgl){e.n=4;break}throw new u("MapboxGL failed to load properly");case 4:return e.a(2)}}),e,this)}))),function(){return p.apply(this,arguments)})},{key:"loadSuperMap3D",value:(l=y(f().m((function e(){var t,n,r;return f().w((function(e){for(;;)switch(e.n){case 0:if(!this.loadingState.supermap3d){e.n=1;break}return e.a(2);case 1:if(t="supermap3d",!this.loadingPromises.has(t)){e.n=2;break}return e.a(2,this.loadingPromises.get(t));case 2:return n=this._loadSuperMap3D(),this.loadingPromises.set(t,n),e.p=3,e.n=4,n;case 4:this.loadingState.supermap3d=!0,e.n=6;break;case 5:throw e.p=5,r=e.v,this.loadingPromises.delete(t),r;case 6:return e.a(2)}}),e,this,[[3,5]])}))),function(){return l.apply(this,arguments)})},{key:"_loadSuperMap3D",value:(s=y(f().m((function e(){var t,n;return f().w((function(e){for(;;)switch(e.n){case 0:if(t=this.config.supermap3d){e.n=1;break}throw new u("SuperMap3D configuration not found");case 1:if(void 0===window.SuperMap3D){e.n=2;break}return e.a(2);case 2:return n=[this.loadJS(t.js)],t.css&&n.push(this.loadCSS(t.css)),e.n=3,Promise.all(n);case 3:if(void 0!==window.SuperMap3D){e.n=4;break}throw new u("SuperMap3D failed to load properly");case 4:return e.a(2)}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"loadIClientMapboxGL",value:(c=y(f().m((function e(){var t,n,r;return f().w((function(e){for(;;)switch(e.n){case 0:if(!this.loadingState.iclientMapboxgl){e.n=1;break}return e.a(2);case 1:if(t="iclientMapboxgl",!this.loadingPromises.has(t)){e.n=2;break}return e.a(2,this.loadingPromises.get(t));case 2:return n=this._loadIClientMapboxGL(),this.loadingPromises.set(t,n),e.p=3,e.n=4,n;case 4:this.loadingState.iclientMapboxgl=!0,e.n=6;break;case 5:throw e.p=5,r=e.v,this.loadingPromises.delete(t),r;case 6:return e.a(2)}}),e,this,[[3,5]])}))),function(){return c.apply(this,arguments)})},{key:"_loadIClientMapboxGL",value:(a=y(f().m((function e(){var t,n;return f().w((function(e){for(;;)switch(e.n){case 0:if(t=this.config.iclientMapboxgl){e.n=1;break}throw new u("iClient MapboxGL configuration not found");case 1:return e.n=2,this.loadMapboxGL();case 2:if(!(n=window.mapboxgl)||!n.supermap){e.n=3;break}return e.a(2);case 3:return e.n=4,Promise.all([this.loadCSS(t.css),this.loadJS(t.js)]);case 4:if(n&&n.supermap){e.n=5;break}throw new u("iClient for MapboxGL failed to load properly");case 5:return e.a(2)}}),e,this)}))),function(){return a.apply(this,arguments)})},{key:"loadIClient3D",value:(i=y(f().m((function e(){var t,n,r;return f().w((function(e){for(;;)switch(e.n){case 0:if(!this.loadingState.iclient3d){e.n=1;break}return e.a(2);case 1:if(t="iclient3d",!this.loadingPromises.has(t)){e.n=2;break}return e.a(2,this.loadingPromises.get(t));case 2:return n=this._loadIClient3D(),this.loadingPromises.set(t,n),e.p=3,e.n=4,n;case 4:this.loadingState.iclient3d=!0,e.n=6;break;case 5:throw e.p=5,r=e.v,this.loadingPromises.delete(t),r;case 6:return e.a(2)}}),e,this,[[3,5]])}))),function(){return i.apply(this,arguments)})},{key:"_loadIClient3D",value:(o=y(f().m((function e(){var t,n;return f().w((function(e){for(;;)switch(e.n){case 0:if(t=this.config.iclient3d){e.n=1;break}throw new u("iClient3D configuration not found");case 1:return e.n=2,this.loadSuperMap3D();case 2:if(!(n=window.SuperMap3D)||!n.iClient){e.n=3;break}return e.a(2);case 3:return e.n=4,this.loadJS(t.js);case 4:n&&n.iClient||console.warn("iClient3D validation may need adjustment based on actual structure");case 5:return e.a(2)}}),e,this)}))),function(){return o.apply(this,arguments)})},{key:"getLoadingState",value:function(){return d({},this.loadingState)}},{key:"reset",value:function(){this.loadingState={},this.loadingPromises.clear()}}],r=[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}],n&&v(t.prototype,n),r&&v(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,o,i,a,c,s,l,p,h}();function w(e){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function S(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,j(r.key),r)}}function O(e,t,n){return(t=j(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e){var t=function(e){if("object"!=w(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=w(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==w(t)?t:t+""}var P=function(){return e=function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),O(this,"eventListeners",new Map),this.instance=t,this.container=n,this.setupEventForwarding()},t=[{key:"normalizeEvent",value:function(e,t){return{type:t,target:this,originalEvent:e.originalEvent||e,point:e.point,lngLat:e.lngLat,features:e.features}}},{key:"getContainer",value:function(){return this.container}},{key:"on",value:function(e,t){return this.eventListeners.has(e)||this.eventListeners.set(e,new Set),this.eventListeners.get(e).add(t),this}},{key:"off",value:function(e,t){if(!this.eventListeners.has(e))return this;var n=this.eventListeners.get(e);return t?n.delete(t):n.clear(),0===n.size&&this.eventListeners.delete(e),this}},{key:"fire",value:function(e,t){if(!this.eventListeners.has(e))return this;var n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){O(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({type:e,target:this},t);return this.eventListeners.get(e).forEach((function(t){try{t(n)}catch(t){console.error("Error in event listener for ".concat(e,":"),t)}})),this}},{key:"getOriginalInstance",value:function(){return this.instance}},{key:"validateLayerConfig",value:function(e){if(!e.id)throw new u("Layer must have an id");if(!e.type)throw new u("Layer must have a type")}},{key:"validateSourceConfig",value:function(e){if(!e.type)throw new u("Source must have a type")}},{key:"safeExecute",value:function(e,t){try{return e()}catch(e){throw new u("".concat(t,": ").concat(e.message))}}},{key:"checkInstance",value:function(){if(!this.instance)throw new u("Map instance is not available")}}],t&&S(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();function M(e){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},M(e)}function D(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,x(r.key),r)}}function x(e){var t=function(e){if("object"!=M(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==M(t)?t:t+""}function E(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(E=function(){return!!e})()}function I(e){return I=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},I(e)}function C(e,t){return C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},C(e,t)}var L=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t,n){return t=I(t),function(e,t){if(t&&("object"==M(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,E()?Reflect.construct(t,n||[],I(e).constructor):t.apply(e,n))}(this,t,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&C(e,t)}(t,e),n=t,r=[{key:"setupEventForwarding",value:function(){var e=this;this.instance&&["load","idle","remove","render","resize","webglcontextlost","webglcontextrestored","dataloading","data","tiledataloading","sourcedataloading","styledataloading","sourcedata","styledata","boxzoomcancel","boxzoomstart","boxzoomend","touchcancel","touchmove","touchend","touchstart","click","contextmenu","dblclick","mousemove","mouseup","mousedown","mouseout","mouseover","movestart","move","moveend","zoomstart","zoom","zoomend","rotatestart","rotate","rotateend","dragstart","drag","dragend","pitchstart","pitch","pitchend","wheel"].forEach((function(t){e.instance.on(t,(function(n){var r=e.normalizeEvent(n,t);e.fire(t,r)}))}))}},{key:"getCenter",value:function(){this.checkInstance();var e=this.instance.getCenter();return[e.lng,e.lat]}},{key:"setCenter",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.setCenter(e)}),"Failed to set center"),this}},{key:"getZoom",value:function(){return this.checkInstance(),this.instance.getZoom()}},{key:"setZoom",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.setZoom(e)}),"Failed to set zoom"),this}},{key:"getBearing",value:function(){return this.checkInstance(),this.instance.getBearing()}},{key:"setBearing",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.setBearing(e)}),"Failed to set bearing"),this}},{key:"getPitch",value:function(){return this.checkInstance(),this.instance.getPitch()}},{key:"setPitch",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.setPitch(e)}),"Failed to set pitch"),this}},{key:"addLayer",value:function(e){var t=this;return this.checkInstance(),this.validateLayerConfig(e),this.safeExecute((function(){t.instance.addLayer(e)}),"Failed to add layer ".concat(e.id)),this}},{key:"removeLayer",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.getLayer(e)&&t.instance.removeLayer(e)}),"Failed to remove layer ".concat(e)),this}},{key:"getLayer",value:function(e){return this.checkInstance(),this.instance.getLayer(e)}},{key:"moveLayer",value:function(e,t){var n=this;return this.checkInstance(),this.safeExecute((function(){n.instance.moveLayer(e,t)}),"Failed to move layer ".concat(e)),this}},{key:"addSource",value:function(e,t){var n=this;return this.checkInstance(),this.validateSourceConfig(t),this.safeExecute((function(){n.instance.addSource(e,t)}),"Failed to add source ".concat(e)),this}},{key:"removeSource",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.getSource(e)&&t.instance.removeSource(e)}),"Failed to remove source ".concat(e)),this}},{key:"getSource",value:function(e){return this.checkInstance(),this.instance.getSource(e)}},{key:"resize",value:function(){var e=this;return this.checkInstance(),this.safeExecute((function(){e.instance.resize()}),"Failed to resize map"),this}},{key:"remove",value:function(){var e=this;this.instance&&(this.safeExecute((function(){e.instance.remove()}),"Failed to remove map"),this.instance=null),this.eventListeners.clear()}},{key:"setStyle",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.setStyle(e)}),"Failed to set style"),this}},{key:"getStyle",value:function(){return this.checkInstance(),this.instance.getStyle()}},{key:"flyTo",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.flyTo(e)}),"Failed to fly to location"),this}},{key:"jumpTo",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.jumpTo(e)}),"Failed to jump to location"),this}},{key:"fitBounds",value:function(e,t){var n=this;return this.checkInstance(),this.safeExecute((function(){n.instance.fitBounds(e,t)}),"Failed to fit bounds"),this}},{key:"queryRenderedFeatures",value:function(e,t){var n=this;return this.checkInstance(),this.safeExecute((function(){return n.instance.queryRenderedFeatures(e,t)}),"Failed to query rendered features")}},{key:"querySourceFeatures",value:function(e,t){var n=this;return this.checkInstance(),this.safeExecute((function(){return n.instance.querySourceFeatures(e,t)}),"Failed to query source features")}},{key:"unproject",value:function(e){this.checkInstance();var t=this.instance.unproject(e);return[t.lng,t.lat]}},{key:"project",value:function(e){this.checkInstance();var t=this.instance.project(e);return[t.x,t.y]}}],r&&D(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(P);function T(e){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}function _(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,F(r.key),r)}}function F(e){var t=function(e){if("object"!=T(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=T(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==T(t)?t:t+""}function G(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(G=function(){return!!e})()}function z(e){return z=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},z(e)}function B(e,t){return B=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},B(e,t)}var R=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),function(e,t,n){return t=z(t),function(e,t){if(t&&("object"==T(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,G()?Reflect.construct(t,n||[],z(e).constructor):t.apply(e,n))}(this,t,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&B(e,t)}(t,e),n=t,(r=[{key:"setupEventForwarding",value:function(){var e=this;if(this.instance){var t=this.instance.scene,n=this.instance.camera;if(t&&t.canvas){var r=new window.SuperMap3D.ScreenSpaceEventHandler(t.canvas);r.setInputAction((function(n){var r=t.pickPosition(n.position),o=window.SuperMap3D.Cartographic.fromCartesian(r);if(o){var i=window.SuperMap3D.Math.toDegrees(o.longitude),a=window.SuperMap3D.Math.toDegrees(o.latitude),c=e.normalizeEvent({originalEvent:n,point:[n.position.x,n.position.y],lngLat:[i,a]},"click");e.fire("click",c)}}),window.SuperMap3D.ScreenSpaceEventType.LEFT_CLICK),r.setInputAction((function(t){var n=e.normalizeEvent({originalEvent:t,point:[t.endPosition.x,t.endPosition.y]},"mousemove");e.fire("mousemove",n)}),window.SuperMap3D.ScreenSpaceEventType.MOUSE_MOVE),n&&(n.moveStart.addEventListener((function(){e.fire("movestart",{type:"movestart",target:e})})),n.moveEnd.addEventListener((function(){e.fire("moveend",{type:"moveend",target:e})}))),t.postRender&&t.postRender.addEventListener((function(){e.fire("render",{type:"render",target:e})}))}}}},{key:"getCenter",value:function(){this.checkInstance();var e=this.instance.camera,t=window.SuperMap3D.Cartographic.fromCartesian(e.position);return[window.SuperMap3D.Math.toDegrees(t.longitude),window.SuperMap3D.Math.toDegrees(t.latitude)]}},{key:"setCenter",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){var n=window.SuperMap3D.Cartesian3.fromDegrees(e[0],e[1],1e3);t.instance.camera.setView({destination:n})}),"Failed to set center"),this}},{key:"getZoom",value:function(){this.checkInstance();var e=this.instance.camera,t=window.SuperMap3D.Cartographic.fromCartesian(e.position).height;return Math.log2(40075016.686/t)-8}},{key:"setZoom",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){var n=40075016.686/Math.pow(2,e+8),r=t.instance.camera,o=window.SuperMap3D.Cartographic.fromCartesian(r.position),i=window.SuperMap3D.Math.toDegrees(o.longitude),a=window.SuperMap3D.Math.toDegrees(o.latitude),c=window.SuperMap3D.Cartesian3.fromDegrees(i,a,n);r.setView({destination:c})}),"Failed to set zoom"),this}},{key:"getBearing",value:function(){this.checkInstance();var e=this.instance.camera;return window.SuperMap3D.Math.toDegrees(e.heading)}},{key:"setBearing",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){var n=t.instance.camera,r=window.SuperMap3D.Math.toRadians(e);n.setView({orientation:{heading:r,pitch:n.pitch,roll:n.roll}})}),"Failed to set bearing"),this}},{key:"getPitch",value:function(){this.checkInstance();var e=this.instance.camera;return window.SuperMap3D.Math.toDegrees(e.pitch)}},{key:"setPitch",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){var n=t.instance.camera,r=window.SuperMap3D.Math.toRadians(e);n.setView({orientation:{heading:n.heading,pitch:r,roll:n.roll}})}),"Failed to set pitch"),this}},{key:"addLayer",value:function(e){var t=this;return this.checkInstance(),this.validateLayerConfig(e),this.safeExecute((function(){switch(t.instance.scene,e.type){case"imagery":t.addImageryLayer(e);break;case"s3m":t.addS3MLayer(e);break;case"terrain":t.addTerrainLayer(e);break;case"mvt":t.addMVTLayer(e);break;default:console.warn("Layer type ".concat(e.type," is not supported in 3D mode"))}}),"Failed to add layer ".concat(e.id)),this}},{key:"addImageryLayer",value:function(e){this.instance.scene;var t=this.instance.imageryLayers;if(e.source&&"object"===T(e.source)&&e.source.url){var n=new window.SuperMap3D.SuperMapImageryProvider({url:e.source.url});t.addImageryProvider(n)}}},{key:"addS3MLayer",value:function(e){var t=this.instance.scene;e.source&&"object"===T(e.source)&&e.source.url&&t.addS3MTilesLayerByScp(e.source.url,{name:e.id})}},{key:"addTerrainLayer",value:function(e){if(e.source&&"object"===T(e.source)&&e.source.url){var t=new window.SuperMap3D.SuperMapTerrainProvider({url:e.source.url,isSct:!0});this.instance.terrainProvider=t}}},{key:"addMVTLayer",value:function(e){var t=this.instance.scene;e.source&&"object"===T(e.source)&&e.source.url&&t.addVectorTilesMap({url:e.source.url,name:e.id,viewer:this.instance})}},{key:"removeLayer",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){for(var n=t.instance.scene,r=t.instance.imageryLayers,o=0;o<r.length;o++){var i=r.get(o);if(i.name===e){r.remove(i);break}}n.layers&&n.layers.removeAll()}),"Failed to remove layer ".concat(e)),this}},{key:"getLayer",value:function(e){return this.checkInstance(),null}},{key:"moveLayer",value:function(e,t){return this.checkInstance(),console.warn("moveLayer is not fully supported in 3D mode"),this}},{key:"addSource",value:function(e,t){return this.checkInstance(),this.validateSourceConfig(t),console.warn("addSource in 3D mode is handled through layer configuration"),this}},{key:"removeSource",value:function(e){return this.checkInstance(),console.warn("removeSource in 3D mode is handled through layer management"),this}},{key:"getSource",value:function(e){return this.checkInstance(),null}},{key:"resize",value:function(){var e=this;return this.checkInstance(),this.safeExecute((function(){e.instance.resize()}),"Failed to resize scene"),this}},{key:"remove",value:function(){var e=this;this.instance&&(this.safeExecute((function(){e.instance.destroy()}),"Failed to destroy scene"),this.instance=null),this.eventListeners.clear()}},{key:"flyTo",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.camera.flyTo(e)}),"Failed to fly to location"),this}},{key:"setView",value:function(e){var t=this;return this.checkInstance(),this.safeExecute((function(){t.instance.camera.setView(e)}),"Failed to set view"),this}},{key:"getScene",value:function(){return this.checkInstance(),this.instance.scene}},{key:"getCamera",value:function(){return this.checkInstance(),this.instance.camera}}])&&_(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(P),A=["type","container"],V=["type","center","zoom","bearing","pitch"];function q(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function K(e){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},K(e)}function N(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,s=Object.create(u.prototype);return J(s,"_invoke",function(n,r,o){var i,c,u,s=0,f=o||[],l=!1,p={p:0,n:0,v:e,a:y,f:y.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function y(n,r){for(c=n,u=r,t=0;!l&&s&&!o&&t<f.length;t++){var o,i=f[t],y=p.p,h=i[2];n>3?(o=h===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=y&&((o=n<2&&y<i[1])?(c=0,p.v=r,p.n=i[1]):y<h&&(o=n<3||i[0]>r||r>h)&&(i[4]=n,i[5]=r,p.n=h,c=0))}if(o||n>1)return a;throw l=!0,r}return function(o,f,h){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,h),c=f,u=h;(t=c<2?e:u)||!l;){i||(c?c<3?(c>1&&(p.n=-1),y(c,u)):p.n=u:p.v=u);try{if(s=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(l=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{s=1}}return{value:t,done:l}}}(n,o,i),!0),s}var a={};function c(){}function u(){}function s(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(J(t={},r,(function(){return this})),t),l=s.prototype=c.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,J(e,o,"GeneratorFunction")),e.prototype=Object.create(l),e}return u.prototype=s,J(l,"constructor",s),J(s,"constructor",u),u.displayName="GeneratorFunction",J(s,o,"GeneratorFunction"),J(l),J(l,o,"Generator"),J(l,r,(function(){return this})),J(l,"toString",(function(){return"[object Generator]"})),(N=function(){return{w:i,m:p}})()}function J(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}J=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){J(e,t,(function(e){return this._invoke(t,n,e)}))};i("next",0),i("throw",1),i("return",2)}},J(e,t,n,r)}function Z(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function H(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){Z(i,r,o,a,c,"next",e)}function c(e){Z(i,r,o,a,c,"throw",e)}a(void 0)}))}}function U(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function W(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?U(Object(n),!0).forEach((function(t){X(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):U(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Q(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Y(r.key),r)}}function X(e,t,n){return(t=Y(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Y(e){var t=function(e){if("object"!=K(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=K(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==K(t)?t:t+""}var $=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),X(this,"defaultConfig",{mapboxgl:{js:"https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.js",css:"https://cdnjs.cloudflare.com/ajax/libs/mapbox-gl/1.13.2/mapbox-gl.css"},supermap3d:{js:"./iclient-3d/SuperMap3D/SuperMap3D.js",css:"./iclient-3d/SuperMap3D/Widgets/widgets.css"},iclientMapboxgl:{js:"./iclient-mapboxgl/iclient-mapboxgl.js",css:"./iclient-mapboxgl/iclient-mapboxgl.css"},iclient3d:{js:"./iclient-3d/deps.js"}}),this.loader=g.getInstance(),this.loader.setConfig(this.defaultConfig)}return t=e,n=[{key:"setConfig",value:function(e){this.defaultConfig=W(W({},this.defaultConfig),e),this.loader.setConfig(this.defaultConfig)}},{key:"createMap",value:(i=H(N().m((function e(t){var n;return N().w((function(e){for(;;)switch(e.n){case 0:if(this.validateConfig(t),n=this.resolveContainer(t.container),t.type!==c.MAP_2D){e.n=1;break}return e.a(2,this.create2DMap(t,n));case 1:if(t.type!==c.MAP_3D){e.n=2;break}return e.a(2,this.create3DMap(t,n));case 2:return e.a(2,this.autoDetectMapType(t,n));case 3:return e.a(2)}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"autoDetectMapType",value:(o=H(N().m((function e(t,n){var r,o;return N().w((function(e){for(;;)switch(e.n){case 0:if(!this.detect3DFeatures(t)){e.n=1;break}return console.log("Auto-detected 3D features, creating 3D map"),r=W(W({},t),{},{type:c.MAP_3D}),e.a(2,this.create3DMap(r,n));case 1:return console.log("Auto-detected 2D features, creating 2D map"),o=W(W({},t),{},{type:c.MAP_2D}),e.a(2,this.create2DMap(o,n));case 2:return e.a(2)}}),e,this)}))),function(e,t){return o.apply(this,arguments)})},{key:"detect3DFeatures",value:function(e){var t=!!(e.scene3DOnly||e.terrainProvider||e.shadows||e.globe||e.skyBox||e.sceneMode||e.pitch>0),n=e.style&&"object"===K(e.style)&&e.style.layers&&e.style.layers.some((function(e){return"fill-extrusion"===e.type||"hillshade"===e.type||"raster-dem"===e.type}));return t||n}},{key:"create2DMap",value:function(){var e=H(N().m((function e(t,n){var r,o,i;return N().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,Promise.all([this.loader.loadMapboxGL(),this.loader.loadIClientMapboxGL()]);case 1:if(r=window.mapboxgl){e.n=2;break}throw new u("MapboxGL failed to load");case 2:return o=this.prepare2DConfig(t),i=new r.Map(o),e.n=3,new Promise((function(e,t){i.on("load",(function(){return e()})),i.on("error",(function(e){var n;return t(new u("Map load error: ".concat((null===(n=e.error)||void 0===n?void 0:n.message)||"Unknown error")))})),setTimeout((function(){return t(new u("Map load timeout"))}),3e4)}));case 3:return e.a(2,new L(i,n))}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"create3DMap",value:function(){var e=H(N().m((function e(t,n){var r,o,i,a=this;return N().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,Promise.all([this.loader.loadSuperMap3D(),this.loader.loadIClient3D()]);case 1:if(r=window.SuperMap3D){e.n=2;break}throw new u("SuperMap3D failed to load");case 2:return o=this.prepare3DConfig(t,n),i=new r.Viewer(n,o),e.n=3,new Promise((function(e,n){if(i.scene){if(t.center){var o=r.Cartesian3.fromDegrees(t.center[0],t.center[1],t.zoom?a.zoomToHeight(t.zoom):1e4);i.camera.setView({destination:o,orientation:{heading:r.Math.toRadians(t.bearing||0),pitch:r.Math.toRadians(t.pitch||-90),roll:0}})}e()}else n(new u("Failed to initialize 3D scene"))}));case 3:return e.a(2,new R(i,n))}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"prepare2DConfig",value:function(e){e.type;var t=e.container,n=q(e,A);return W({container:"string"==typeof t?t:t.id||"map",center:e.center||[0,0],zoom:e.zoom||0,bearing:e.bearing||0,pitch:e.pitch||0},n)}},{key:"prepare3DConfig",value:function(e,t){e.type,e.center,e.zoom,e.bearing,e.pitch;var n=q(e,V);return W({animation:!1!==e.animation,timeline:!1!==e.timeline,baseLayerPicker:!1!==e.baseLayerPicker,fullscreenButton:!1!==e.fullscreenButton,geocoder:!1!==e.geocoder,homeButton:!1!==e.homeButton,sceneModePicker:!1!==e.sceneModePicker,navigationHelpButton:!1!==e.navigationHelpButton},n)}},{key:"zoomToHeight",value:function(e){return 40075016.686/Math.pow(2,e+8)}},{key:"resolveContainer",value:function(e){if("string"==typeof e){var t=document.getElementById(e)||document.querySelector(e);if(!t)throw new u("Container element not found: ".concat(e));return t}return e}},{key:"validateConfig",value:function(e){if(!e)throw new u("Map configuration is required");if(!e.container)throw new u("Container is required");if(e.type&&!Object.values(c).includes(e.type))throw new u("Invalid map type: ".concat(e.type))}},{key:"getLoadingState",value:function(){return this.loader.getLoadingState()}}],r=[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}],n&&Q(t.prototype,n),r&&Q(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,o,i}();function ee(e){return ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ee(e)}function te(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ne(r.key),r)}}function ne(e){var t=function(e){if("object"!=ee(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=ee(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ee(t)?t:t+""}var re=function(){return e=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.mapInstance=t,this.proxy=this.createProxy()},(t=[{key:"createProxy",value:function(){var e=this;return new Proxy(this.mapInstance,{get:function(t,n,r){if(n in t){var o=Reflect.get(t,n,r);return"function"==typeof o?o.bind(t):o}var i=t.getOriginalInstance();if(i&&n in i){var a=i[n];return"function"==typeof a?a.bind(i):a}return e.handleSpecialProperties(t,n)},set:function(e,t,n,r){if(t in e)return Reflect.set(e,t,n,r);var o=e.getOriginalInstance();if(o&&t in o)return o[t]=n,!0;throw new u("Property '".concat(String(t),"' does not exist on map instance"))},has:function(e,t){if(t in e)return!0;var n=e.getOriginalInstance();return!(!n||!(t in n))},ownKeys:function(e){var t=new Set;Object.getOwnPropertyNames(e).forEach((function(e){return t.add(e)})),Object.getOwnPropertySymbols(e).forEach((function(e){return t.add(e)}));var n=e.getOriginalInstance();return n&&(Object.getOwnPropertyNames(n).forEach((function(e){return t.add(e)})),Object.getOwnPropertySymbols(n).forEach((function(e){return t.add(e)}))),Array.from(t)},getOwnPropertyDescriptor:function(e,t){var n=Object.getOwnPropertyDescriptor(e,t);if(n)return n;var r=e.getOriginalInstance();return r&&(n=Object.getOwnPropertyDescriptor(r,t))?n:void 0}})}},{key:"handleSpecialProperties",value:function(e,t){var n=e.getOriginalInstance();switch(t){case"map":case"viewer":case"scene":return n;case"version":return this.getVersion(n);case"mapType":return this.getMapType(n);case"loaded":return this.isLoaded(n);case"container":return e.getContainer();default:throw new u("Property or method '".concat(t,"' does not exist on map instance. ")+"Available methods: ".concat(this.getAvailableMethods(e).join(", ")))}}},{key:"getVersion",value:function(e){return e&&"string"==typeof e.version?e.version:void 0!==window.mapboxgl?window.mapboxgl.version||"unknown":void 0!==window.SuperMap3D&&window.SuperMap3D.VERSION||"unknown"}},{key:"getMapType",value:function(e){if(e){if(e.getStyle||e.addLayer)return"2d";if(e.scene||e.camera)return"3d"}return"unknown"}},{key:"isLoaded",value:function(e){if(e){if("function"==typeof e.loaded)return e.loaded();if(e.scene)return!0}return!1}},{key:"getAvailableMethods",value:function(e){for(var t=[],n=e;n&&n!==Object.prototype;)Object.getOwnPropertyNames(n).forEach((function(e){"function"!=typeof n[e]||t.includes(e)||t.push(e)})),n=Object.getPrototypeOf(n);var r=e.getOriginalInstance();if(r)for(var o=r;o&&o!==Object.prototype;)Object.getOwnPropertyNames(o).forEach((function(e){"function"!=typeof o[e]||t.includes(e)||t.push(e)})),o=Object.getPrototypeOf(o);return t.sort()}},{key:"getProxy",value:function(){return this.proxy}},{key:"getMapInstance",value:function(){return this.mapInstance}},{key:"destroy",value:function(){this.mapInstance&&this.mapInstance.remove(),this.mapInstance=null,this.proxy=null}}])&&te(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();function oe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ie(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(n),!0).forEach((function(t){pe(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):oe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ae(e){return ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ae(e)}function ce(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,s=Object.create(u.prototype);return ue(s,"_invoke",function(n,r,o){var i,c,u,s=0,f=o||[],l=!1,p={p:0,n:0,v:e,a:y,f:y.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function y(n,r){for(c=n,u=r,t=0;!l&&s&&!o&&t<f.length;t++){var o,i=f[t],y=p.p,h=i[2];n>3?(o=h===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=y&&((o=n<2&&y<i[1])?(c=0,p.v=r,p.n=i[1]):y<h&&(o=n<3||i[0]>r||r>h)&&(i[4]=n,i[5]=r,p.n=h,c=0))}if(o||n>1)return a;throw l=!0,r}return function(o,f,h){if(s>1)throw TypeError("Generator is already running");for(l&&1===f&&y(f,h),c=f,u=h;(t=c<2?e:u)||!l;){i||(c?c<3?(c>1&&(p.n=-1),y(c,u)):p.n=u:p.v=u);try{if(s=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(l=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{s=1}}return{value:t,done:l}}}(n,o,i),!0),s}var a={};function c(){}function u(){}function s(){}t=Object.getPrototypeOf;var f=[][r]?t(t([][r]())):(ue(t={},r,(function(){return this})),t),l=s.prototype=c.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,ue(e,o,"GeneratorFunction")),e.prototype=Object.create(l),e}return u.prototype=s,ue(l,"constructor",s),ue(s,"constructor",u),u.displayName="GeneratorFunction",ue(s,o,"GeneratorFunction"),ue(l),ue(l,o,"Generator"),ue(l,r,(function(){return this})),ue(l,"toString",(function(){return"[object Generator]"})),(ce=function(){return{w:i,m:p}})()}function ue(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}ue=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){ue(e,t,(function(e){return this._invoke(t,n,e)}))};i("next",0),i("throw",1),i("return",2)}},ue(e,t,n,r)}function se(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function fe(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){se(i,r,o,a,c,"next",e)}function c(e){se(i,r,o,a,c,"throw",e)}a(void 0)}))}}function le(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ye(r.key),r)}}function pe(e,t,n){return(t=ye(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ye(e){var t=function(e){if("object"!=ae(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=ae(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ae(t)?t:t+""}var he=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},t=[{key:"setConfig",value:function(e){this.factory.setConfig(e)}},{key:"createMap",value:(r=fe(ce().m((function e(t){var n,r,o;return ce().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,this.factory.createMap(t);case 1:return n=e.v,r=new re(n),e.a(2,r.getProxy());case 2:if(e.p=2,!((o=e.v)instanceof u)){e.n=3;break}throw o;case 3:throw new u("Failed to create map: ".concat(o.message));case 4:return e.a(2)}}),e,this,[[0,2]])}))),function(e){return r.apply(this,arguments)})},{key:"getLoadingState",value:function(){return this.factory.getLoadingState()}},{key:"preloadSDK",value:(n=fe(ce().m((function e(){var t,n,r=arguments;return ce().w((function(e){for(;;)switch(e.n){case 0:return n=[],"all"!==(t=r.length>0&&void 0!==r[0]?r[0]:"all")&&"2d"!==t||n.push(this.loader.loadMapboxGL(),this.loader.loadIClientMapboxGL()),"all"!==t&&"3d"!==t||n.push(this.loader.loadSuperMap3D(),this.loader.loadIClient3D()),e.n=1,Promise.all(n);case 1:return e.a(2)}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"isSDKLoaded",value:function(e){var t=this.getLoadingState();return"2d"===e?!(!t.mapboxgl||!t.iclientMapboxgl):!(!t.supermap3d||!t.iclient3d)}},{key:"getVersion",value:function(){return"1.0.0"}}],t&&le(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r}();function de(){return(de=fe(ce().m((function e(t){return ce().w((function(e){for(;;)if(0===e.n)return e.a(2,he.createMap(t))}),e)})))).apply(this,arguments)}function ve(){return(ve=fe(ce().m((function e(t){return ce().w((function(e){for(;;)if(0===e.n)return e.a(2,he.createMap(ie(ie({},t),{},{type:c.MAP_2D})))}),e)})))).apply(this,arguments)}function be(){return(be=fe(ce().m((function e(t){return ce().w((function(e){for(;;)if(0===e.n)return e.a(2,he.createMap(ie(ie({},t),{},{type:c.MAP_3D})))}),e)})))).apply(this,arguments)}function me(){return(me=fe(ce().m((function e(t){return ce().w((function(e){for(;;)if(0===e.n)return e.a(2,he.createMap(t))}),e)})))).apply(this,arguments)}pe(he,"factory",$.getInstance()),pe(he,"loader",g.getInstance());const ge=he;return"undefined"!=typeof window&&(window.SGMapSDK=he,window.createMap=function(e){return de.apply(this,arguments)},window.create2DMap=function(e){return ve.apply(this,arguments)},window.create3DMap=function(e){return be.apply(this,arguments)},window.createAutoMap=function(e){return me.apply(this,arguments)}),t.default})()));