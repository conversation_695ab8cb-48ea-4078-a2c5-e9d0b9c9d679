/**
 * 地图代理 - 通过Proxy机制统一不同SDK的接口访问
 */

import { IMapInstance, MapSDKError } from '../types';

export class MapProxy {
  private mapInstance: IMapInstance;
  private proxy: any;

  constructor(mapInstance: IMapInstance) {
    this.mapInstance = mapInstance;
    this.proxy = this.createProxy();
  }

  /**
   * 创建代理对象
   */
  private createProxy(): any {
    return new Proxy(this.mapInstance, {
      get: (target: IMapInstance, property: string | symbol, receiver: any) => {
        // 首先检查统一接口中是否存在该属性
        if (property in target) {
          const value = Reflect.get(target, property, receiver);

          // 如果是方法，绑定正确的this上下文
          if (typeof value === 'function') {
            return value.bind(target);
          }

          return value;
        }

        // 检查原始实例中是否存在该属性
        const originalInstance = target.getOriginalInstance();
        if (originalInstance && property in originalInstance) {
          const value = originalInstance[property];

          // 如果是方法，绑定原始实例的this上下文
          if (typeof value === 'function') {
            return value.bind(originalInstance);
          }

          return value;
        }

        // 特殊处理一些常用的属性和方法
        return this.handleSpecialProperties(target, property as string);
      },

      set: (target: IMapInstance, property: string | symbol, value: any, receiver: any) => {
        // 首先尝试设置统一接口的属性
        if (property in target) {
          return Reflect.set(target, property, value, receiver);
        }

        // 尝试设置原始实例的属性
        const originalInstance = target.getOriginalInstance();
        if (originalInstance && property in originalInstance) {
          originalInstance[property] = value;
          return true;
        }

        // 如果都不存在，抛出错误
        throw new MapSDKError(`Property '${String(property)}' does not exist on map instance`);
      },

      has: (target: IMapInstance, property: string | symbol) => {
        // 检查统一接口
        if (property in target) {
          return true;
        }

        // 检查原始实例
        const originalInstance = target.getOriginalInstance();
        if (originalInstance && property in originalInstance) {
          return true;
        }

        return false;
      },

      ownKeys: (target: IMapInstance) => {
        const keys = new Set<string | symbol>();

        // 添加统一接口的键
        Object.getOwnPropertyNames(target).forEach(key => keys.add(key));
        Object.getOwnPropertySymbols(target).forEach(key => keys.add(key));

        // 添加原始实例的键
        const originalInstance = target.getOriginalInstance();
        if (originalInstance) {
          Object.getOwnPropertyNames(originalInstance).forEach(key => keys.add(key));
          Object.getOwnPropertySymbols(originalInstance).forEach(key => keys.add(key));
        }

        return Array.from(keys);
      },

      getOwnPropertyDescriptor: (target: IMapInstance, property: string | symbol) => {
        // 首先检查统一接口
        let descriptor = Object.getOwnPropertyDescriptor(target, property);
        if (descriptor) {
          return descriptor;
        }

        // 检查原始实例
        const originalInstance = target.getOriginalInstance();
        if (originalInstance) {
          descriptor = Object.getOwnPropertyDescriptor(originalInstance, property);
          if (descriptor) {
            return descriptor;
          }
        }

        return undefined;
      }
    });
  }

  /**
   * 处理特殊属性和方法
   */
  private handleSpecialProperties(target: IMapInstance, property: string): any {
    const originalInstance = target.getOriginalInstance();

    switch (property) {
      // 常用的地图属性别名
      case 'map':
      case 'viewer':
      case 'scene':
        return originalInstance;

      // 版本信息
      case 'version':
        return this.getVersion(originalInstance);

      // 类型信息
      case 'mapType':
        return this.getMapType(originalInstance);

      // 是否已加载
      case 'loaded':
        return this.isLoaded(originalInstance);

      // 容器元素
      case 'container':
        return target.getContainer();

      // 如果属性不存在，提供友好的错误信息
      default:
        throw new MapSDKError(
          `Property or method '${property}' does not exist on map instance. ` +
          `Available methods: ${this.getAvailableMethods(target).join(', ')}`
        );
    }
  }

  /**
   * 获取版本信息
   */
  private getVersion(originalInstance: any): string {
    if (originalInstance && typeof originalInstance.version === 'string') {
      return originalInstance.version;
    }

    // 尝试从全局对象获取版本
    if (typeof (window as any).mapboxgl !== 'undefined') {
      return (window as any).mapboxgl.version || 'unknown';
    }

    if (typeof (window as any).SuperMap3D !== 'undefined') {
      return (window as any).SuperMap3D.VERSION || 'unknown';
    }

    return 'unknown';
  }

  /**
   * 获取地图类型
   */
  private getMapType(originalInstance: any): string {
    if (originalInstance) {
      // 检查是否是MapboxGL实例
      if (originalInstance.getStyle || originalInstance.addLayer) {
        return '2d';
      }

      // 检查是否是SuperMap3D实例
      if (originalInstance.scene || originalInstance.camera) {
        return '3d';
      }
    }

    return 'unknown';
  }

  /**
   * 检查是否已加载
   */
  private isLoaded(originalInstance: any): boolean {
    if (originalInstance) {
      // MapboxGL的loaded状态
      if (typeof originalInstance.loaded === 'function') {
        return originalInstance.loaded();
      }

      // SuperMap3D通常在创建后就是可用的
      if (originalInstance.scene) {
        return true;
      }
    }

    return false;
  }

  /**
   * 获取可用方法列表
   */
  private getAvailableMethods(target: IMapInstance): string[] {
    const methods: string[] = [];

    // 获取统一接口的方法
    let obj = target;
    while (obj && obj !== Object.prototype) {
      Object.getOwnPropertyNames(obj).forEach(name => {
        if (typeof (obj as any)[name] === 'function' && !methods.includes(name)) {
          methods.push(name);
        }
      });
      obj = Object.getPrototypeOf(obj);
    }

    // 获取原始实例的方法
    const originalInstance = target.getOriginalInstance();
    if (originalInstance) {
      let originalObj = originalInstance;
      while (originalObj && originalObj !== Object.prototype) {
        Object.getOwnPropertyNames(originalObj).forEach(name => {
          if (typeof originalObj[name] === 'function' && !methods.includes(name)) {
            methods.push(name);
          }
        });
        originalObj = Object.getPrototypeOf(originalObj);
      }
    }

    return methods.sort();
  }

  /**
   * 获取代理对象
   */
  public getProxy(): any {
    return this.proxy;
  }

  /**
   * 获取原始地图实例
   */
  public getMapInstance(): IMapInstance {
    return this.mapInstance;
  }

  /**
   * 销毁代理
   */
  public destroy(): void {
    if (this.mapInstance) {
      this.mapInstance.remove();
    }
    this.mapInstance = null as any;
    this.proxy = null;
  }
}
