<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化地图测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }

        #map {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px 0;
        }

        .controls {
            margin: 10px 0;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #005a87;
        }

        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>最小化地图测试</h1>

    <div class="controls">
        <button onclick="testBasic()">基础测试</button>
        <button onclick="testMapboxGL()">测试MapboxGL</button>
        <button onclick="testCreate2D()">创建2D地图</button>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <div id="map"></div>

    <div class="log" id="log"></div>

    <!-- 加载统一地图SDK (调试版本) -->
    <script src="../dist/sg-map-sdk.debug.js"></script>

    <script>
        function log(message) {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logEl.textContent += `[${time}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[${time}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function testBasic() {
            log('=== 基础测试开始 ===');

            // 检查SDK
            if (typeof window.SGMapSDK !== 'undefined') {
                log('✓ SGMapSDK 已加载');
                log(`版本: ${window.SGMapSDK.getVersion()}`);
            } else {
                log('✗ SGMapSDK 未加载');
                return;
            }

            // 检查全局函数
            if (typeof window.createMap !== 'undefined') {
                log('✓ createMap 函数可用');
            } else {
                log('✗ createMap 函数不可用');
            }

            log('=== 基础测试完成 ===');
        }

        async function testMapboxGL() {
            log('=== MapboxGL测试开始 ===');

            try {
                // 配置SDK使用本地文件
                window.SGMapSDK.setConfig({
                    mapboxgl: {
                        js: '../iclient-mapboxgl/mapbox-gl.js',
                        css: '../iclient-mapboxgl/mapbox-gl.css'
                    }
                });
                log('✓ SDK配置已设置');

                // 预加载MapboxGL
                log('开始加载MapboxGL...');
                await window.SGMapSDK.preloadSDK('2d');
                log('✓ MapboxGL加载完成');

                // 检查MapboxGL
                if (typeof window.mapboxgl !== 'undefined') {
                    log(`✓ MapboxGL已加载，版本: ${window.mapboxgl.version || 'unknown'}`);
                } else {
                    log('✗ MapboxGL未加载');
                }

            } catch (error) {
                log(`✗ MapboxGL加载失败: ${error.message}`);
            }

            log('=== MapboxGL测试完成 ===');
        }

        async function testCreate2D() {
            log('=== 2D地图创建测试开始 ===');

            try {
                // 先确保MapboxGL已加载
                await testMapboxGL();

                log('开始创建2D地图...');

                // 使用最简单的配置，不依赖外部样式
                const config = {
                    type: '2d',
                    container: 'map',
                    center: [116.3974, 39.9093],
                    zoom: 10,
                    // 使用内置的简单样式，避免网络请求
                    style: {
                        version: 8,
                        sources: {},
                        layers: [{
                            id: 'background',
                            type: 'background',
                            paint: {
                                'background-color': '#f0f0f0'
                            }
                        }]
                    }
                };

                log(`地图配置: ${JSON.stringify(config, null, 2)}`);

                // 添加超时监控
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error('自定义超时：地图创建超过 10 秒'));
                    }, 10000);
                });

                const createPromise = window.createMap(config);

                log('等待地图创建...');
                const map = await Promise.race([createPromise, timeoutPromise]);

                log('✓ 2D地图创建成功！');
                log(`地图中心: ${map.getCenter()}`);
                log(`缩放级别: ${map.getZoom()}`);

                // 测试地图事件
                if (map.on) {
                    map.on('click', () => log('地图被点击'));
                    log('✓ 事件监听器已添加');
                }

            } catch (error) {
                log(`✗ 2D地图创建失败: ${error.message}`);
                log(`错误堆栈: ${error.stack}`);

                // 检查可能的原因
                log('--- 错误诊断 ---');
                if (typeof window.mapboxgl === 'undefined') {
                    log('原因: MapboxGL 未加载');
                } else if (!document.getElementById('map')) {
                    log('原因: 地图容器不存在');
                } else {
                    log('原因: 其他未知错误，请检查控制台');
                }
            }

            log('=== 2D地图创建测试完成 ===');
        }

        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            testBasic();
        });
    </script>
</body>
</html>
